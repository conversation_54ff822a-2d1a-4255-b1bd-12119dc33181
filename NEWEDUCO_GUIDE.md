# NewEduco Guide

## System Overview

Educo is a comprehensive educational management system designed for Tunisian schools to manage academic, administrative, and operational processes. This document outlines the structure, features, and requirements needed to replicate the system using modern technologies.

## Core Modules & Features

### 1. Academic Management

#### Class and Course Structure

- **Classes Management**
  - Create and manage class hierarchies
  - Assign students to classes/groups
  - Allocate classrooms to classes
  - Track class capacity and enrollment

- **Subjects and Courses**
  - Subject creation with multilingual support (Arabic/French)
  - Course scheduling and timetable management
  - Subject grouping by department/category
  - Language-specific subject configuration
  - Subject coefficient management for grading

- **Online Learning**
  - Virtual course creation and management
  - File upload support (PDF, DOC, DOCX with Google Docs viewer)
  - Video embedding capabilities
  - Homework assignment and submission system
  - Quiz/examination system
  - Online assignment tracking
  - Virtual classroom integration (Google Meet, Zoom links)

#### Academic Planning

- **Academic Year Management**
  - Academic year configuration
  - Term/Period planning and management
  - Class scheduling across periods
  - Year cloning functionality

- **Student Progress**
  - Grade management with multiple examination types
  - Student report generation (multilingual - Arabic/French)
  - Class performance reports
  - Academic tracking across periods
  - Ranking system within classes

### 2. Student Management

#### Enrollment and Records

- **Student Information**
  - Personal details management
  - Academic history tracking
  - Family information and contacts
  - Document management

- **Attendance**
  - Class attendance tracking
  - Transportation attendance monitoring
  - Absence reporting and statistics
  - Attendance visualization (charts/reports)

#### Student Services

- **Cafeteria Services**
  - Meal planning and scheduling
  - Student meal tracking
  - Meal complaints management
  - Menu management

- **Transportation**
  - Route management
  - Vehicle tracking and assignment
  - Journey planning
  - Transportation attendance

### 3. Financial Management

#### Fee Management

- **Services and Payments**
  - Service categories configuration
  - Payment schedule creation
  - Payment tracking
  - Student service assignment

- **Billing**
  - Fee collection tracking
  - Payment history
  - Revenue forecasting
  - Financial reporting

### 4. Communication

#### Notifications

- **System Notifications**
  - Module-specific notifications
  - Real-time updates
  - SMS notification capabilities
  - Firebase push notifications
  - Email notifications

- **Parent Communication**
  - Parent request management
  - Announcement system
  - Messaging between parents and staff
  - Document sharing

### 5. System Configuration

#### Settings

- **System Configuration**
  - School information management
  - Academic year settings
  - Notification preferences
  - Module activation/deactivation

#### Security

- **Access Control**
  - User authentication
  - Role-based access control
  - Activity logging
  - Permission management

## Technical Requirements

### Frontend

- **Framework**: React with TypeScript
- **UI Components**: Tailwind CSS, Shadcn UI
- **State Management**: React zustan
- **Form Handling**: React inertiajs Form
- **Data Fetching**: inertiajs Fetch API
- **PDF Generation**: Client-side PDF generation library

### Backend

- **Framework**: Laravel 11.x
- **API**: inertiajs
- **Authentication**: inertiajs
- **Real-time Updates**: Laravel Reverb

### Database

- **Primary Database**: MySQL/MariaDB
- **Schema Requirements**:
  - Users (roles: admin, teacher, student, parent)
  - Schools
  - Academic Years
  - Periods/Terms
  - Classes
  - Subjects
  - Students
  - Teachers
  - Attendance Records
  - Grades/Marks
  - Examinations
  - Transportation (vehicles, routes, journeys)
  - Cafeteria (menus, meals)
  - Financial Records
  - Notifications
  - System Settings

### Multilingual Support

- Arabic (RTL)
- French (LTR)
- Dynamic language switching
- Language-specific templates for reports

### PDF Generation

- Support for both RTL (Arabic) and LTR (French) layouts
- Custom styling for different report types
- School branding integration
- Dynamic content generation

### External Integrations

- SMS Gateway for notifications
- Firebase for push notifications
- Video conferencing tools (Google Meet, Zoom)

## Deployment Requirements

### System Requirements

- PHP 8.2 or higher
- MySQL/MariaDB database
- Web server (Apache/Nginx)
- Node.js for frontend build
- Composer for PHP dependency management
- NPM/Yarn for JavaScript dependency management

### Environment Configuration

- Database connection settings
- Mail server configuration
- SMS gateway API credentials
- Firebase configuration
- Storage configuration for file uploads
- Session management settings

## Data Migration Strategy

### Data Export

- Identify critical data from existing system
- Create data export scripts for each entity
- Validate data integrity before migration

### Data Import

- Create data import scripts for the new system
- Map old data structures to new schema
- Implement validation during import
- Preserve relationships between entities

### Verification

- Implement data verification procedures
- Compare record counts between systems
- Validate critical business rules post-migration

## Development Roadmap

1. **Phase 1: Core System Setup**
   - Database schema design
   - User authentication and authorization
   - Basic school configuration

2. **Phase 2: Academic Management**
   - Class and subject management
   - Academic year and period configuration
   - Grade management

3. **Phase 3: Student Management**
   - Student records
   - Attendance tracking
   - Report generation

4. **Phase 4: Services**
   - Transportation management
   - Cafeteria services
   - Financial management

5. **Phase 5: Communication**
   - Notification system
   - Parent-teacher communication
   - Announcements

6. **Phase 6: Online Learning**
   - Virtual courses
   - Homework management
   - Quiz system

7. **Phase 7: Integration & Testing**
   - External service integration
   - System-wide testing
   - Performance optimization

## Best Practices

### Security best practices

- Implement strong authentication
- Use HTTPS for all communications
- Implement proper input validation
- Follow OWASP security guidelines
- Regular security audits

### Performance

- Implement caching strategies
- Optimize database queries
- Use lazy loading for resources
- Implement pagination for large datasets
- Asset optimization (minification, compression)

### Maintainability

- Follow PSR standards for PHP code
- Use TypeScript for type safety
- Implement comprehensive logging
- Document API endpoints
- Use version control effectively

## Support and Maintenance

### Monitoring

- Implement error logging and monitoring
- Set up performance monitoring
- Database health checks
- Service availability monitoring

### Backup Strategy

- Regular database backups
- File storage backups
- Backup verification procedures
- Disaster recovery plan

### Update Procedures

- Regular security updates
- Feature updates
- Database schema updates
- Configuration updates

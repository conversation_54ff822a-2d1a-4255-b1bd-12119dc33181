# NewEduco - Educational Management System

A comprehensive educational management system designed specifically for Tunisian schools to manage academic, administrative, and operational processes. Built with modern web technologies for scalability, performance, and user experience.

## 🚀 Technology Stack

### Backend
- **Laravel 12.x** - PHP framework for robust backend development
- **Inertia.js** - Modern monolith approach for seamless SPA experience
- **MySQL/MariaDB** - Primary database for data storage
- **Laravel Reverb** - Real-time WebSocket communication

### Frontend
- **React 19** - Modern UI library with latest features
- **TypeScript** - Type-safe JavaScript for better development experience
- **Tailwind CSS 4.0** - Utility-first CSS framework
- **shadcn/ui** - High-quality, accessible UI components
- **Zustand** - Lightweight state management
- **Vite 6.0** - Fast build tool and development server

### Development Tools
- **Laravel Pint** - PHP code style fixer
- **ESLint & Prettier** - JavaScript/TypeScript code formatting
- **Pest** - PHP testing framework
- **<PERSON><PERSON> Sail** - Docker development environment

## 🎯 Core Features

### 📚 Academic Management
- **Class & Course Structure**: Hierarchical class management, student assignments, classroom allocation
- **Subject Management**: Multilingual support (Arabic/French), course scheduling, coefficient management
- **Grade Management**: Multiple examination types, comprehensive reporting, ranking systems
- **Academic Planning**: Year configuration, term planning, schedule management

### 👥 Student Management
- **Enrollment & Records**: Personal details, academic history, family information, document management
- **Attendance System**: Class and transportation attendance with real-time tracking and reporting
- **Progress Tracking**: Academic performance monitoring, multilingual report generation

### 💻 Online Learning Platform
- **Virtual Courses**: Course creation and management with multimedia support
- **File Management**: PDF, DOC, DOCX uploads with Google Docs viewer integration
- **Assignment System**: Homework distribution, submission tracking, and grading
- **Assessment Tools**: Quiz and examination system with automated scoring
- **Virtual Classrooms**: Google Meet and Zoom integration

### 🚌 Student Services
- **Transportation**: Route management, vehicle tracking, journey planning, attendance monitoring
- **Cafeteria**: Meal planning, tracking, menu management, complaint handling

### 💰 Financial Management
- **Fee Structure**: Service categories, payment schedules, tracking systems
- **Billing & Payments**: Collection tracking, payment history, revenue forecasting
- **Financial Reporting**: Comprehensive financial analytics and reporting

### 📢 Communication System
- **Multi-channel Notifications**: SMS, email, Firebase push notifications
- **Parent-Teacher Communication**: Messaging platform, announcement system
- **Real-time Updates**: Live notifications and system updates

### 🌐 Multilingual Support
- **Arabic (RTL)** and **French (LTR)** with seamless language switching
- **Dynamic Content**: Language-specific templates and layouts
- **PDF Generation**: RTL/LTR support for reports and documents

## 🛠️ Installation & Setup

### Prerequisites
- PHP 8.2 or higher
- Node.js 18+ and npm/yarn
- MySQL/MariaDB database
- Composer

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd new-educo
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install JavaScript dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database setup**
   ```bash
   # Configure your database in .env file
   php artisan migrate
   php artisan db:seed
   ```

6. **Start development servers**
   ```bash
   composer run dev
   # This runs: Laravel server + Queue worker + Vite dev server
   ```

### Alternative Development Commands

```bash
# Frontend only
npm run dev

# Backend only
php artisan serve

# With SSR support
composer run dev:ssr

# Build for production
npm run build
npm run build:ssr
```

## 📁 Project Structure

```
new-educo/
├── app/                    # Laravel application code
│   ├── Http/Controllers/   # API and web controllers
│   ├── Models/            # Eloquent models
│   └── Providers/         # Service providers
├── database/              # Database migrations and seeders
├── resources/             # Frontend resources
│   ├── js/               # React TypeScript code
│   │   ├── components/   # Reusable UI components
│   │   ├── pages/        # Inertia.js pages
│   │   ├── layouts/      # Page layouts
│   │   ├── store/        # Zustand stores
│   │   └── types/        # TypeScript definitions
│   ├── css/              # Stylesheets
│   └── views/            # Blade templates
├── routes/               # Application routes
├── scripts/              # Project management scripts
│   ├── prd.txt          # Product Requirements Document
│   └── example_prd.txt  # PRD template
├── tasks/                # TaskMaster AI generated tasks
└── public/               # Public assets
```

## 🧪 Testing

```bash
# Run PHP tests
composer test
# or
php artisan test

# Run with coverage
php artisan test --coverage

# Frontend linting and formatting
npm run lint
npm run format
npm run types
```

## 📋 Development Roadmap

The project follows a 10-phase development approach:

1. **Foundation & Authentication** - Core system setup and user management
2. **Academic Management Core** - Classes, subjects, basic grading
3. **Student Management & Attendance** - Student records and tracking
4. **Enhanced Academic Features** - Advanced grading and reporting
5. **Online Learning Platform** - Virtual courses and assignments
6. **Student Services** - Transportation and cafeteria management
7. **Financial Management** - Fee tracking and billing
8. **Communication & Notifications** - Real-time messaging system
9. **Advanced Features & Optimization** - Performance and analytics
10. **Integration & Polish** - Final testing and deployment

## 🔧 Configuration

### Environment Variables
Key environment variables to configure:

```env
# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=new_educo
DB_USERNAME=root
DB_PASSWORD=

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=

# SMS Gateway
SMS_GATEWAY_URL=
SMS_GATEWAY_API_KEY=

# Firebase
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=
```

### Inertia.js Configuration
The application uses Inertia.js for seamless SPA experience. Configuration is in `config/inertia.php`.

## 🚀 Deployment

### Production Build
```bash
npm run build:ssr
php artisan optimize
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Server Requirements
- PHP 8.2+
- MySQL/MariaDB
- Web server (Apache/Nginx)
- SSL certificate (recommended)
- Node.js (for SSR)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style
- PHP: Follow PSR-12 standards (enforced by Laravel Pint)
- JavaScript/TypeScript: ESLint + Prettier configuration
- Commit messages: Use conventional commits format

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the [documentation](docs/)
- Review the [PRD](scripts/prd.txt) for detailed requirements

## 🙏 Acknowledgments

- Laravel community for the excellent framework
- React and TypeScript teams for modern frontend development
- shadcn/ui for beautiful, accessible components
- Inertia.js for bridging backend and frontend seamlessly

---

**NewEduco** - Empowering education through technology 🎓

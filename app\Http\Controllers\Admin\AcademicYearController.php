<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\School;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AcademicYearController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        $school = School::first(); // For now, get the first school

        $academicYears = AcademicYear::with('periods')
            ->forSchool($school->id)
            ->orderBy('start_date', 'desc')
            ->get();

        return Inertia::render('Admin/AcademicYears/Index', [
            'academicYears' => $academicYears,
            'school' => $school,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $school = School::first();

        return Inertia::render('Admin/AcademicYears/Create', [
            'school' => $school,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $school = School::first();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        $validated['school_id'] = $school->id;

        $academicYear = AcademicYear::create($validated);

        // If this is set as current, update others
        if ($validated['is_current'] ?? false) {
            $academicYear->setCurrent();
        }

        return redirect()->route('admin.academic-years.index')
            ->with('success', 'Academic year created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(AcademicYear $academicYear): Response
    {
        $academicYear->load('periods', 'school');

        return Inertia::render('Admin/AcademicYears/Show', [
            'academicYear' => $academicYear,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AcademicYear $academicYear): Response
    {
        return Inertia::render('Admin/AcademicYears/Edit', [
            'academicYear' => $academicYear,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AcademicYear $academicYear)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        $academicYear->update($validated);

        // If this is set as current, update others
        if ($validated['is_current'] ?? false) {
            $academicYear->setCurrent();
        }

        return redirect()->route('admin.academic-years.index')
            ->with('success', 'Academic year updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AcademicYear $academicYear)
    {
        $academicYear->delete();

        return redirect()->route('admin.academic-years.index')
            ->with('success', 'Academic year deleted successfully.');
    }

    /**
     * Set an academic year as current.
     */
    public function setCurrent(AcademicYear $academicYear)
    {
        $academicYear->setCurrent();

        return redirect()->route('admin.academic-years.index')
            ->with('success', 'Academic year set as current successfully.');
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ClassController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $school = School::first(); // For now, get the first school
        $academicYearId = $request->get('academic_year_id');
        $gradeLevel = $request->get('grade_level');

        $query = SchoolClass::with(['school', 'academicYear'])
            ->forSchool($school->id);

        if ($academicYearId) {
            $query->forAcademicYear($academicYearId);
        }

        if ($gradeLevel) {
            $query->byGradeLevel($gradeLevel);
        }

        $classes = $query->orderBy('grade_level')
            ->orderBy('section')
            ->get();

        $academicYears = AcademicYear::forSchool($school->id)
            ->orderBy('start_date', 'desc')
            ->get();

        $gradeLevels = SchoolClass::getGradeLevels();

        return Inertia::render('Admin/Classes/Index', [
            'classes' => $classes,
            'academicYears' => $academicYears,
            'gradeLevels' => $gradeLevels,
            'filters' => [
                'academic_year_id' => $academicYearId,
                'grade_level' => $gradeLevel,
            ],
            'school' => $school,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $school = School::first();
        $academicYears = AcademicYear::forSchool($school->id)
            ->active()
            ->orderBy('start_date', 'desc')
            ->get();

        $gradeLevels = SchoolClass::getGradeLevels();
        $sections = SchoolClass::getSections();

        return Inertia::render('Admin/Classes/Create', [
            'academicYears' => $academicYears,
            'gradeLevels' => $gradeLevels,
            'sections' => $sections,
            'school' => $school,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $school = School::first();

        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'grade_level' => 'required|string|max:255',
            'section' => 'nullable|string|max:10',
            'capacity' => 'required|integer|min:1|max:100',
            'classroom' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $validated['school_id'] = $school->id;

        // Check for duplicate class name within the same academic year
        $existingClass = SchoolClass::where('school_id', $school->id)
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('name', $validated['name'])
            ->first();

        if ($existingClass) {
            return back()->withErrors(['name' => 'A class with this name already exists for the selected academic year.']);
        }

        SchoolClass::create($validated);

        return redirect()->route('admin.classes.index')
            ->with('success', 'Class created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SchoolClass $class): Response
    {
        $class->load(['school', 'academicYear']);

        return Inertia::render('Admin/Classes/Show', [
            'class' => $class,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SchoolClass $class): Response
    {
        $class->load(['school', 'academicYear']);
        $school = School::first();

        $academicYears = AcademicYear::forSchool($school->id)
            ->active()
            ->orderBy('start_date', 'desc')
            ->get();

        $gradeLevels = SchoolClass::getGradeLevels();
        $sections = SchoolClass::getSections();

        return Inertia::render('Admin/Classes/Edit', [
            'class' => $class,
            'academicYears' => $academicYears,
            'gradeLevels' => $gradeLevels,
            'sections' => $sections,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SchoolClass $class)
    {
        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'grade_level' => 'required|string|max:255',
            'section' => 'nullable|string|max:10',
            'capacity' => 'required|integer|min:1|max:100',
            'classroom' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Check for duplicate class name within the same academic year (excluding current class)
        $existingClass = SchoolClass::where('school_id', $class->school_id)
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('name', $validated['name'])
            ->where('id', '!=', $class->id)
            ->first();

        if ($existingClass) {
            return back()->withErrors(['name' => 'A class with this name already exists for the selected academic year.']);
        }

        $class->update($validated);

        return redirect()->route('admin.classes.index')
            ->with('success', 'Class updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SchoolClass $class)
    {
        $class->delete();

        return redirect()->route('admin.classes.index')
            ->with('success', 'Class deleted successfully.');
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\Period;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PeriodController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $academicYearId = $request->get('academic_year_id');
        $academicYear = null;

        if ($academicYearId) {
            $academicYear = AcademicYear::findOrFail($academicYearId);
            $periods = Period::with('academicYear')
                ->forAcademicYear($academicYearId)
                ->orderBy('order')
                ->get();
        } else {
            $periods = Period::with('academicYear')
                ->orderBy('academic_year_id', 'desc')
                ->orderBy('order')
                ->get();
        }

        $academicYears = AcademicYear::orderBy('start_date', 'desc')->get();

        return Inertia::render('Admin/Periods/Index', [
            'periods' => $periods,
            'academicYears' => $academicYears,
            'selectedAcademicYear' => $academicYear,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): Response
    {
        $academicYearId = $request->get('academic_year_id');
        $academicYears = AcademicYear::active()->orderBy('start_date', 'desc')->get();

        $selectedAcademicYear = null;
        if ($academicYearId) {
            $selectedAcademicYear = AcademicYear::findOrFail($academicYearId);
        }

        return Inertia::render('Admin/Periods/Create', [
            'academicYears' => $academicYears,
            'selectedAcademicYear' => $selectedAcademicYear,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'order' => 'required|integer|min:1',
            'type' => 'required|in:term,semester,quarter',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        // Check if order is unique within the academic year
        $existingPeriod = Period::where('academic_year_id', $validated['academic_year_id'])
            ->where('order', $validated['order'])
            ->first();

        if ($existingPeriod) {
            return back()->withErrors(['order' => 'This order already exists for the selected academic year.']);
        }

        $period = Period::create($validated);

        // If this is set as current, update others
        if ($validated['is_current'] ?? false) {
            $period->setCurrent();
        }

        return redirect()->route('admin.periods.index', ['academic_year_id' => $validated['academic_year_id']])
            ->with('success', 'Period created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Period $period): Response
    {
        $period->load('academicYear');

        return Inertia::render('Admin/Periods/Show', [
            'period' => $period,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Period $period): Response
    {
        $period->load('academicYear');
        $academicYears = AcademicYear::active()->orderBy('start_date', 'desc')->get();

        return Inertia::render('Admin/Periods/Edit', [
            'period' => $period,
            'academicYears' => $academicYears,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Period $period)
    {
        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'order' => 'required|integer|min:1',
            'type' => 'required|in:term,semester,quarter',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'is_current' => 'boolean',
        ]);

        // Check if order is unique within the academic year (excluding current period)
        $existingPeriod = Period::where('academic_year_id', $validated['academic_year_id'])
            ->where('order', $validated['order'])
            ->where('id', '!=', $period->id)
            ->first();

        if ($existingPeriod) {
            return back()->withErrors(['order' => 'This order already exists for the selected academic year.']);
        }

        $period->update($validated);

        // If this is set as current, update others
        if ($validated['is_current'] ?? false) {
            $period->setCurrent();
        }

        return redirect()->route('admin.periods.index', ['academic_year_id' => $validated['academic_year_id']])
            ->with('success', 'Period updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Period $period)
    {
        $academicYearId = $period->academic_year_id;
        $period->delete();

        return redirect()->route('admin.periods.index', ['academic_year_id' => $academicYearId])
            ->with('success', 'Period deleted successfully.');
    }

    /**
     * Set a period as current.
     */
    public function setCurrent(Period $period)
    {
        $period->setCurrent();

        return redirect()->route('admin.periods.index', ['academic_year_id' => $period->academic_year_id])
            ->with('success', 'Period set as current successfully.');
    }
}

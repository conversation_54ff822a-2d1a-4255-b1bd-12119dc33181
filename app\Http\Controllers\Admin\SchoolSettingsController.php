<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\School;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class SchoolSettingsController extends Controller
{
    /**
     * Display the school settings form.
     */
    public function index()
    {
        $school = School::first();

        return Inertia::render('Admin/SchoolSettings', [
            'school' => $school,
            'supportedLanguages' => [
                ['value' => 'ar', 'label' => __('school.arabic_language')],
                ['value' => 'fr', 'label' => __('school.french_language')],
                ['value' => 'en', 'label' => __('school.english_language')],
            ],
            'months' => [
                ['value' => 'January', 'label' => __('school.january')],
                ['value' => 'February', 'label' => __('school.february')],
                ['value' => 'March', 'label' => __('school.march')],
                ['value' => 'April', 'label' => __('school.april')],
                ['value' => 'May', 'label' => __('school.may')],
                ['value' => 'June', 'label' => __('school.june')],
                ['value' => 'July', 'label' => __('school.july')],
                ['value' => 'August', 'label' => __('school.august')],
                ['value' => 'September', 'label' => __('school.september')],
                ['value' => 'October', 'label' => __('school.october')],
                ['value' => 'November', 'label' => __('school.november')],
                ['value' => 'December', 'label' => __('school.december')],
            ],
            'timezones' => [
                ['value' => 'Africa/Tunis', 'label' => __('school.tunisia_timezone')],
                ['value' => 'Europe/Paris', 'label' => __('school.paris_timezone')],
                ['value' => 'UTC', 'label' => __('school.utc_timezone')],
            ],
            'currencies' => [
                ['value' => 'TND', 'label' => __('school.tunisian_dinar')],
                ['value' => 'EUR', 'label' => __('school.euro')],
                ['value' => 'USD', 'label' => __('school.us_dollar')],
            ],
        ]);
    }

    /**
     * Store or update school settings.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'address' => 'required|string',
            'address_ar' => 'nullable|string',
            'address_fr' => 'nullable|string',
            'city' => 'required|string|max:255',
            'state_province' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:255',
            'academic_year_start_month' => 'required|string|max:255',
            'academic_year_end_month' => 'required|string|max:255',
            'terms_per_year' => 'required|integer|min:1|max:4',
            'default_language' => 'required|string|in:ar,fr,en',
            'supported_languages' => 'required|array|min:1',
            'supported_languages.*' => 'string|in:ar,fr,en',
            'timezone' => 'required|string',
            'currency' => 'required|string|max:10',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'is_active' => 'boolean',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('school-logos', 'public');
            $validated['logo_path'] = $logoPath;
        }

        $school = School::first();

        if ($school) {
            // Delete old logo if new one is uploaded
            if (isset($validated['logo_path']) && $school->logo_path) {
                Storage::disk('public')->delete($school->logo_path);
            }

            $school->update($validated);
        } else {
            $school = School::create($validated);
        }

        return redirect()->back()->with('success', 'School settings updated successfully.');
    }

    /**
     * Remove the school logo.
     */
    public function removeLogo()
    {
        $school = School::first();

        if ($school && $school->logo_path) {
            Storage::disk('public')->delete($school->logo_path);
            $school->update(['logo_path' => null]);
        }

        return redirect()->back()->with('success', 'Logo removed successfully.');
    }
}

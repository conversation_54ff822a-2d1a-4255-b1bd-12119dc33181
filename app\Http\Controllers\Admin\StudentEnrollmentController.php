<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\StudentEnrollment;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class StudentEnrollmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $school = School::first(); // For now, get the first school
        $academicYearId = $request->get('academic_year_id');
        $classId = $request->get('class_id');
        $status = $request->get('status');

        $query = StudentEnrollment::with(['student', 'schoolClass', 'academicYear'])
            ->forSchool($school->id);

        if ($academicYearId) {
            $query->forAcademicYear($academicYearId);
        }

        if ($classId) {
            $query->forClass($classId);
        }

        if ($status) {
            $query->byStatus($status);
        }

        $enrollments = $query->orderBy('created_at', 'desc')->get();

        // Get filter options
        $academicYears = AcademicYear::forSchool($school->id)
            ->orderBy('start_date', 'desc')
            ->get();

        $classes = SchoolClass::forSchool($school->id)
            ->active()
            ->orderBy('grade_level')
            ->orderBy('section')
            ->get();

        $statuses = StudentEnrollment::getStatuses();

        // Get enrollment statistics
        $stats = StudentEnrollment::getEnrollmentStats($school->id, $academicYearId);

        return Inertia::render('Admin/StudentEnrollments/Index', [
            'enrollments' => $enrollments,
            'academicYears' => $academicYears,
            'classes' => $classes,
            'statuses' => $statuses,
            'stats' => $stats,
            'filters' => [
                'academic_year_id' => $academicYearId,
                'class_id' => $classId,
                'status' => $status,
            ],
            'school' => $school,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $school = School::first();

        $academicYears = AcademicYear::forSchool($school->id)
            ->active()
            ->orderBy('start_date', 'desc')
            ->get();

        $students = User::where('role', 'student')
            ->orderBy('name')
            ->get();

        $classes = SchoolClass::forSchool($school->id)
            ->active()
            ->orderBy('grade_level')
            ->orderBy('section')
            ->get();

        $statuses = StudentEnrollment::getStatuses();

        return Inertia::render('Admin/StudentEnrollments/Create', [
            'academicYears' => $academicYears,
            'students' => $students,
            'classes' => $classes,
            'statuses' => $statuses,
            'school' => $school,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $school = School::first();

        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'student_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'enrollment_date' => 'required|date',
            'withdrawal_date' => 'nullable|date|after_or_equal:enrollment_date',
            'status' => 'required|in:enrolled,withdrawn,transferred,graduated',
            'notes' => 'nullable|string',
            'is_repeating' => 'boolean',
            'previous_school' => 'nullable|string|max:255',
            'emergency_contacts' => 'nullable|array',
        ]);

        $validated['school_id'] = $school->id;

        // Generate unique student number
        $validated['student_number'] = StudentEnrollment::generateStudentNumber($school->id, $validated['academic_year_id']);

        // Check for duplicate enrollment
        $existingEnrollment = StudentEnrollment::where('school_id', $school->id)
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('student_id', $validated['student_id'])
            ->where('class_id', $validated['class_id'])
            ->first();

        if ($existingEnrollment) {
            return back()->withErrors(['student_id' => 'This student is already enrolled in this class for the selected academic year.']);
        }

        // Validate that the user has the student role
        $student = User::find($validated['student_id']);
        if (!$student || $student->role !== 'student') {
            return back()->withErrors(['student_id' => 'Selected user is not a student.']);
        }

        // Check class capacity
        $class = SchoolClass::find($validated['class_id']);
        $currentEnrollments = StudentEnrollment::where('class_id', $validated['class_id'])
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('status', 'enrolled')
            ->count();

        if ($currentEnrollments >= $class->capacity) {
            return back()->withErrors(['class_id' => 'This class has reached its maximum capacity.']);
        }

        StudentEnrollment::create($validated);

        return redirect()->route('admin.student-enrollments.index')
            ->with('success', 'Student enrollment created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(StudentEnrollment $studentEnrollment): Response
    {
        $studentEnrollment->load(['student', 'schoolClass', 'academicYear', 'school']);

        return Inertia::render('Admin/StudentEnrollments/Show', [
            'enrollment' => $studentEnrollment,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StudentEnrollment $studentEnrollment): Response
    {
        $studentEnrollment->load(['student', 'schoolClass', 'academicYear']);
        $school = School::first();

        $academicYears = AcademicYear::forSchool($school->id)
            ->active()
            ->orderBy('start_date', 'desc')
            ->get();

        $students = User::where('role', 'student')
            ->orderBy('name')
            ->get();

        $classes = SchoolClass::forSchool($school->id)
            ->active()
            ->orderBy('grade_level')
            ->orderBy('section')
            ->get();

        $statuses = StudentEnrollment::getStatuses();

        return Inertia::render('Admin/StudentEnrollments/Edit', [
            'enrollment' => $studentEnrollment,
            'academicYears' => $academicYears,
            'students' => $students,
            'classes' => $classes,
            'statuses' => $statuses,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StudentEnrollment $studentEnrollment)
    {
        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'student_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'enrollment_date' => 'required|date',
            'withdrawal_date' => 'nullable|date|after_or_equal:enrollment_date',
            'status' => 'required|in:enrolled,withdrawn,transferred,graduated',
            'notes' => 'nullable|string',
            'is_repeating' => 'boolean',
            'previous_school' => 'nullable|string|max:255',
            'emergency_contacts' => 'nullable|array',
        ]);

        // Check for duplicate enrollment (excluding current enrollment)
        $existingEnrollment = StudentEnrollment::where('school_id', $studentEnrollment->school_id)
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('student_id', $validated['student_id'])
            ->where('class_id', $validated['class_id'])
            ->where('id', '!=', $studentEnrollment->id)
            ->first();

        if ($existingEnrollment) {
            return back()->withErrors(['student_id' => 'This student is already enrolled in this class for the selected academic year.']);
        }

        // Validate that the user has the student role
        $student = User::find($validated['student_id']);
        if (!$student || $student->role !== 'student') {
            return back()->withErrors(['student_id' => 'Selected user is not a student.']);
        }

        // Check class capacity (if changing class)
        if ($validated['class_id'] != $studentEnrollment->class_id) {
            $class = SchoolClass::find($validated['class_id']);
            $currentEnrollments = StudentEnrollment::where('class_id', $validated['class_id'])
                ->where('academic_year_id', $validated['academic_year_id'])
                ->where('status', 'enrolled')
                ->where('id', '!=', $studentEnrollment->id)
                ->count();

            if ($currentEnrollments >= $class->capacity) {
                return back()->withErrors(['class_id' => 'This class has reached its maximum capacity.']);
            }
        }

        $studentEnrollment->update($validated);

        return redirect()->route('admin.student-enrollments.index')
            ->with('success', 'Student enrollment updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StudentEnrollment $studentEnrollment)
    {
        $studentEnrollment->delete();

        return redirect()->route('admin.student-enrollments.index')
            ->with('success', 'Student enrollment deleted successfully.');
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\School;
use App\Models\Subject;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SubjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $school = School::first(); // For now, get the first school
        $category = $request->get('category');
        $gradeLevel = $request->get('grade_level');

        $query = Subject::with('school')
            ->forSchool($school->id);

        if ($category) {
            $query->byCategory($category);
        }

        if ($gradeLevel) {
            $query->forGradeLevel($gradeLevel);
        }

        $subjects = $query->orderBy('category')
            ->orderBy('name')
            ->get();

        $categories = Subject::getCategories();
        $gradeLevels = Subject::getAvailableGradeLevels();

        return Inertia::render('Admin/Subjects/Index', [
            'subjects' => $subjects,
            'categories' => $categories,
            'gradeLevels' => $gradeLevels,
            'filters' => [
                'category' => $category,
                'grade_level' => $gradeLevel,
            ],
            'school' => $school,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $school = School::first();
        $categories = Subject::getCategories();
        $gradeLevels = Subject::getAvailableGradeLevels();

        return Inertia::render('Admin/Subjects/Create', [
            'categories' => $categories,
            'gradeLevels' => $gradeLevels,
            'school' => $school,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $school = School::first();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'code' => 'nullable|string|max:20',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'grade_levels' => 'nullable|array',
            'grade_levels.*' => 'string',
            'coefficient' => 'required|numeric|min:0.1|max:10',
            'weekly_hours' => 'required|integer|min:1|max:20',
            'category' => 'required|in:core,elective,extracurricular',
            'is_active' => 'boolean',
        ]);

        $validated['school_id'] = $school->id;

        // Check for duplicate subject name within the same school
        $existingSubject = Subject::where('school_id', $school->id)
            ->where('name', $validated['name'])
            ->first();

        if ($existingSubject) {
            return back()->withErrors(['name' => 'A subject with this name already exists.']);
        }

        // Check for duplicate subject code if provided
        if (!empty($validated['code'])) {
            $existingCode = Subject::where('school_id', $school->id)
                ->where('code', $validated['code'])
                ->first();

            if ($existingCode) {
                return back()->withErrors(['code' => 'A subject with this code already exists.']);
            }
        }

        Subject::create($validated);

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Subject created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Subject $subject): Response
    {
        $subject->load('school');

        return Inertia::render('Admin/Subjects/Show', [
            'subject' => $subject,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Subject $subject): Response
    {
        $categories = Subject::getCategories();
        $gradeLevels = Subject::getAvailableGradeLevels();

        return Inertia::render('Admin/Subjects/Edit', [
            'subject' => $subject,
            'categories' => $categories,
            'gradeLevels' => $gradeLevels,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Subject $subject)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_fr' => 'nullable|string|max:255',
            'code' => 'nullable|string|max:20',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_fr' => 'nullable|string',
            'grade_levels' => 'nullable|array',
            'grade_levels.*' => 'string',
            'coefficient' => 'required|numeric|min:0.1|max:10',
            'weekly_hours' => 'required|integer|min:1|max:20',
            'category' => 'required|in:core,elective,extracurricular',
            'is_active' => 'boolean',
        ]);

        // Check for duplicate subject name within the same school (excluding current subject)
        $existingSubject = Subject::where('school_id', $subject->school_id)
            ->where('name', $validated['name'])
            ->where('id', '!=', $subject->id)
            ->first();

        if ($existingSubject) {
            return back()->withErrors(['name' => 'A subject with this name already exists.']);
        }

        // Check for duplicate subject code if provided (excluding current subject)
        if (!empty($validated['code'])) {
            $existingCode = Subject::where('school_id', $subject->school_id)
                ->where('code', $validated['code'])
                ->where('id', '!=', $subject->id)
                ->first();

            if ($existingCode) {
                return back()->withErrors(['code' => 'A subject with this code already exists.']);
            }
        }

        $subject->update($validated);

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Subject updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Subject $subject)
    {
        $subject->delete();

        return redirect()->route('admin.subjects.index')
            ->with('success', 'Subject deleted successfully.');
    }
}

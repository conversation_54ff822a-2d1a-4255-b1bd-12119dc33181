<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the role-based dashboard.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();

        // Get role-specific data
        $dashboardData = $this->getDashboardData($user);

        return Inertia::render('dashboard', [
            'user' => $user,
            'dashboardData' => $dashboardData,
        ]);
    }

    /**
     * Get dashboard data based on user role.
     */
    private function getDashboardData($user): array
    {
        return match ($user->role) {
            'admin' => $this->getAdminDashboardData($user),
            'teacher' => $this->getTeacherDashboardData($user),
            'student' => $this->getStudentDashboardData($user),
            'parent' => $this->getParentDashboardData($user),
            default => [],
        };
    }

    /**
     * Get admin dashboard data.
     */
    private function getAdminDashboardData($user): array
    {
        return [
            'role' => 'admin',
            'stats' => [
                'total_users' => \App\Models\User::count(),
                'total_teachers' => \App\Models\User::where('role', 'teacher')->count(),
                'total_students' => \App\Models\User::where('role', 'student')->count(),
                'total_parents' => \App\Models\User::where('role', 'parent')->count(),
            ],
            'recent_activities' => [
                'New teacher registered',
                'Student enrollment completed',
                'System backup completed',
            ],
            'quick_actions' => [
                ['title' => 'Manage Users', 'href' => '#', 'icon' => 'Users'],
                ['title' => 'School Settings', 'href' => route('admin.school-settings'), 'icon' => 'Settings'],
                ['title' => 'Academic Management', 'href' => '#', 'icon' => 'BookOpen'],
                ['title' => 'System Reports', 'href' => '#', 'icon' => 'BarChart'],
            ],
        ];
    }

    /**
     * Get teacher dashboard data.
     */
    private function getTeacherDashboardData($user): array
    {
        return [
            'role' => 'teacher',
            'stats' => [
                'my_classes' => 0, // TODO: Implement when classes are available
                'my_students' => 0, // TODO: Implement when students are available
                'pending_grades' => 0, // TODO: Implement when grades are available
                'attendance_today' => 0, // TODO: Implement when attendance is available
            ],
            'recent_activities' => [
                'Grade entry for Math class',
                'Attendance marked for Class 5A',
                'Assignment created',
            ],
            'quick_actions' => [
                ['title' => 'My Classes', 'href' => '#', 'icon' => 'BookOpen'],
                ['title' => 'Grade Entry', 'href' => '#', 'icon' => 'Edit'],
                ['title' => 'Attendance', 'href' => '#', 'icon' => 'CheckSquare'],
                ['title' => 'Student Reports', 'href' => '#', 'icon' => 'FileText'],
            ],
        ];
    }

    /**
     * Get student dashboard data.
     */
    private function getStudentDashboardData($user): array
    {
        return [
            'role' => 'student',
            'stats' => [
                'my_subjects' => 0, // TODO: Implement when subjects are available
                'current_average' => 0, // TODO: Implement when grades are available
                'attendance_rate' => 0, // TODO: Implement when attendance is available
                'assignments_due' => 0, // TODO: Implement when assignments are available
            ],
            'recent_activities' => [
                'New grade posted for Mathematics',
                'Assignment due tomorrow',
                'Attendance marked',
            ],
            'quick_actions' => [
                ['title' => 'My Grades', 'href' => '#', 'icon' => 'Award'],
                ['title' => 'My Schedule', 'href' => '#', 'icon' => 'Calendar'],
                ['title' => 'Assignments', 'href' => '#', 'icon' => 'FileText'],
                ['title' => 'Attendance', 'href' => '#', 'icon' => 'CheckSquare'],
            ],
        ];
    }

    /**
     * Get parent dashboard data.
     */
    private function getParentDashboardData($user): array
    {
        return [
            'role' => 'parent',
            'stats' => [
                'my_children' => 0, // TODO: Implement when parent-child relationships are available
                'notifications' => 0, // TODO: Implement when notifications are available
                'upcoming_events' => 0, // TODO: Implement when events are available
                'payment_due' => 0, // TODO: Implement when payments are available
            ],
            'recent_activities' => [
                'Child\'s grade updated',
                'School event notification',
                'Payment reminder',
            ],
            'quick_actions' => [
                ['title' => 'Children\'s Progress', 'href' => '#', 'icon' => 'TrendingUp'],
                ['title' => 'School Communications', 'href' => '#', 'icon' => 'MessageSquare'],
                ['title' => 'Events & Calendar', 'href' => '#', 'icon' => 'Calendar'],
                ['title' => 'Payments', 'href' => '#', 'icon' => 'CreditCard'],
            ],
        ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\School;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Switch the application language.
     */
    public function switch(Request $request, string $locale)
    {
        // Get supported languages from school settings
        $school = School::first();
        $supportedLanguages = $school?->supported_languages ?? ['en', 'ar', 'fr'];

        // Validate the requested locale
        if (!in_array($locale, $supportedLanguages)) {
            return redirect()->back()->with('error', 'Unsupported language.');
        }

        // Store the locale in session
        Session::put('locale', $locale);

        // If user is authenticated, update their preferred language
        if ($request->user()) {
            $request->user()->update(['preferred_language' => $locale]);
        }

        return redirect()->back()->with('success', 'Language changed successfully.');
    }

    /**
     * Get available languages for the frontend.
     */
    public function getAvailableLanguages()
    {
        $school = School::first();
        $supportedLanguages = $school?->supported_languages ?? ['en', 'ar', 'fr'];

        $languages = [];
        foreach ($supportedLanguages as $code) {
            $languages[] = [
                'code' => $code,
                'name' => $this->getLanguageName($code),
                'native_name' => $this->getNativeLanguageName($code),
                'direction' => in_array($code, ['ar', 'he', 'fa', 'ur']) ? 'rtl' : 'ltr',
            ];
        }

        return response()->json([
            'languages' => $languages,
            'current' => app()->getLocale(),
        ]);
    }

    /**
     * Get the English name of a language.
     */
    private function getLanguageName(string $code): string
    {
        return match ($code) {
            'ar' => 'Arabic',
            'fr' => 'French',
            'en' => 'English',
            default => ucfirst($code),
        };
    }

    /**
     * Get the native name of a language.
     */
    private function getNativeLanguageName(string $code): string
    {
        return match ($code) {
            'ar' => 'العربية',
            'fr' => 'Français',
            'en' => 'English',
            default => ucfirst($code),
        };
    }
}

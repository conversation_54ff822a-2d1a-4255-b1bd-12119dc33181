<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\Grade;
use App\Models\School;
use App\Models\StudentEnrollment;
use App\Models\Subject;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class GradeController extends Controller
{
    /**
     * Display a listing of the student's grades.
     */
    public function index(Request $request): Response
    {
        $student = auth()->user();
        $school = School::first(); // For now, get the first school
        $academicYearId = $request->get('academic_year_id');
        $subjectId = $request->get('subject_id');
        $gradeType = $request->get('grade_type');

        // Get student's enrollments to filter available academic years and subjects
        $studentEnrollments = StudentEnrollment::where('student_id', $student->id)
            ->where('school_id', $school->id)
            ->where('status', 'enrolled')
            ->with(['academicYear', 'subject', 'schoolClass'])
            ->get();

        if ($studentEnrollments->isEmpty()) {
            return Inertia::render('Student/Grades/Index', [
                'grades' => [],
                'academicYears' => [],
                'subjects' => [],
                'gradeTypes' => Grade::getGradeTypes(),
                'filters' => [],
                'subjectStats' => [],
                'overallStats' => null,
                'message' => 'You are not enrolled in any classes yet. Please contact your administrator.',
            ]);
        }

        $query = Grade::with(['teacher', 'schoolClass', 'subject', 'academicYear'])
            ->where('student_id', $student->id)
            ->where('school_id', $school->id)
            ->where('status', 'published'); // Only show published grades to students

        if ($academicYearId) {
            $query->forAcademicYear($academicYearId);
        }

        if ($subjectId) {
            $query->forSubject($subjectId);
        }

        if ($gradeType) {
            $query->byType($gradeType);
        }

        $grades = $query->orderBy('assessment_date', 'desc')->get();

        // Get filter options from student's enrollments
        $academicYears = $studentEnrollments->pluck('academicYear')->unique('id')->values();
        $subjects = $studentEnrollments->pluck('subject')->unique('id')->values();

        // Calculate subject statistics
        $subjectStats = [];
        foreach ($subjects as $subject) {
            $stats = Grade::getStudentSubjectStats($student->id, $subject->id, $academicYearId);
            if ($stats) {
                $subjectStats[] = [
                    'subject' => $subject,
                    'stats' => $stats,
                ];
            }
        }

        // Calculate overall statistics
        $overallStats = $this->calculateOverallStats($student->id, $academicYearId);

        return Inertia::render('Student/Grades/Index', [
            'grades' => $grades,
            'academicYears' => $academicYears,
            'subjects' => $subjects,
            'gradeTypes' => Grade::getGradeTypes(),
            'filters' => [
                'academic_year_id' => $academicYearId,
                'subject_id' => $subjectId,
                'grade_type' => $gradeType,
            ],
            'subjectStats' => $subjectStats,
            'overallStats' => $overallStats,
            'studentEnrollments' => $studentEnrollments,
        ]);
    }

    /**
     * Display the specified grade.
     */
    public function show(Grade $grade): Response
    {
        $student = auth()->user();

        // Verify student owns this grade and it's published
        if ($grade->student_id !== $student->id || $grade->status !== 'published') {
            abort(403, 'You can only view your own published grades.');
        }

        $grade->load(['teacher', 'schoolClass', 'subject', 'academicYear']);

        return Inertia::render('Student/Grades/Show', [
            'grade' => $grade,
        ]);
    }

    /**
     * Get grades by subject for the student.
     */
    public function bySubject(Request $request, Subject $subject): Response
    {
        $student = auth()->user();
        $academicYearId = $request->get('academic_year_id');

        // Verify student is enrolled in this subject
        $enrollment = StudentEnrollment::where('student_id', $student->id)
            ->where('subject_id', $subject->id)
            ->where('status', 'enrolled')
            ->first();

        if (!$enrollment) {
            abort(403, 'You are not enrolled in this subject.');
        }

        $query = Grade::with(['teacher', 'schoolClass', 'academicYear'])
            ->where('student_id', $student->id)
            ->where('subject_id', $subject->id)
            ->where('status', 'published')
            ->orderBy('assessment_date', 'desc');

        if ($academicYearId) {
            $query->forAcademicYear($academicYearId);
        }

        $grades = $query->get();

        // Get subject statistics
        $stats = Grade::getStudentSubjectStats($student->id, $subject->id, $academicYearId);

        // Get available academic years for this subject
        $academicYears = Grade::where('student_id', $student->id)
            ->where('subject_id', $subject->id)
            ->where('status', 'published')
            ->with('academicYear')
            ->get()
            ->pluck('academicYear')
            ->unique('id')
            ->values();

        return Inertia::render('Student/Grades/BySubject', [
            'subject' => $subject,
            'grades' => $grades,
            'stats' => $stats,
            'academicYears' => $academicYears,
            'selectedAcademicYearId' => $academicYearId,
        ]);
    }

    /**
     * Calculate overall statistics for the student.
     */
    private function calculateOverallStats($studentId, $academicYearId = null)
    {
        $query = Grade::where('student_id', $studentId)
            ->where('status', 'published');

        if ($academicYearId) {
            $query->where('academic_year_id', $academicYearId);
        }

        $grades = $query->get();

        if ($grades->isEmpty()) {
            return null;
        }

        // Calculate weighted average across all subjects
        $subjectAverages = [];
        $subjects = $grades->pluck('subject_id')->unique();

        foreach ($subjects as $subjectId) {
            $subjectGrades = $grades->where('subject_id', $subjectId);
            $totalWeightedScore = 0;
            $totalWeight = 0;

            foreach ($subjectGrades as $grade) {
                $weightedScore = $grade->percentage * $grade->weight;
                $totalWeightedScore += $weightedScore;
                $totalWeight += $grade->weight;
            }

            if ($totalWeight > 0) {
                $subjectAverages[] = $totalWeightedScore / $totalWeight;
            }
        }

        if (empty($subjectAverages)) {
            return null;
        }

        $overallAverage = array_sum($subjectAverages) / count($subjectAverages);

        return [
            'total_grades' => $grades->count(),
            'subjects_count' => count($subjectAverages),
            'overall_average' => round($overallAverage, 2),
            'overall_letter_grade' => Grade::percentageToLetterGrade($overallAverage),
            'overall_gpa' => Grade::percentageToGpaPoints($overallAverage),
            'highest_grade' => $grades->max('percentage'),
            'lowest_grade' => $grades->min('percentage'),
            'grade_distribution' => $this->calculateGradeDistribution($grades),
        ];
    }

    /**
     * Calculate grade distribution for charts.
     */
    private function calculateGradeDistribution($grades)
    {
        $distribution = [
            'A' => 0, 'B' => 0, 'C' => 0, 'D' => 0, 'F' => 0
        ];

        foreach ($grades as $grade) {
            $letterGrade = substr($grade->letter_grade, 0, 1); // Get first letter (A, B, C, D, F)
            if (isset($distribution[$letterGrade])) {
                $distribution[$letterGrade]++;
            }
        }

        return $distribution;
    }
}

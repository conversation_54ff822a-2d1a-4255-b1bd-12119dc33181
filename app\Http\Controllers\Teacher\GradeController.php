<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use App\Models\Grade;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\StudentEnrollment;
use App\Models\Subject;
use App\Models\TeacherAssignment;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class GradeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $teacher = auth()->user();
        $school = School::first(); // For now, get the first school
        $academicYearId = $request->get('academic_year_id');
        $classId = $request->get('class_id');
        $subjectId = $request->get('subject_id');
        $gradeType = $request->get('grade_type');
        $status = $request->get('status');

        // Get teacher's assignments to filter available classes and subjects
        $teacherAssignments = TeacherAssignment::where('teacher_id', $teacher->id)
            ->where('school_id', $school->id)
            ->where('is_active', true)
            ->with(['schoolClass', 'subject', 'academicYear'])
            ->get();

        if ($teacherAssignments->isEmpty()) {
            return Inertia::render('Teacher/Grades/Index', [
                'grades' => [],
                'academicYears' => [],
                'classes' => [],
                'subjects' => [],
                'gradeTypes' => Grade::getGradeTypes(),
                'statuses' => Grade::getStatuses(),
                'filters' => [],
                'message' => 'You are not assigned to any classes yet. Please contact your administrator.',
            ]);
        }

        $query = Grade::with(['student', 'schoolClass', 'subject', 'academicYear'])
            ->where('teacher_id', $teacher->id)
            ->where('school_id', $school->id);

        if ($academicYearId) {
            $query->forAcademicYear($academicYearId);
        }

        if ($classId) {
            $query->forClass($classId);
        }

        if ($subjectId) {
            $query->forSubject($subjectId);
        }

        if ($gradeType) {
            $query->byType($gradeType);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $grades = $query->orderBy('assessment_date', 'desc')->get();

        // Get filter options from teacher's assignments
        $academicYears = $teacherAssignments->pluck('academicYear')->unique('id')->values();
        $classes = $teacherAssignments->pluck('schoolClass')->unique('id')->values();
        $subjects = $teacherAssignments->pluck('subject')->unique('id')->values();

        return Inertia::render('Teacher/Grades/Index', [
            'grades' => $grades,
            'academicYears' => $academicYears,
            'classes' => $classes,
            'subjects' => $subjects,
            'gradeTypes' => Grade::getGradeTypes(),
            'statuses' => Grade::getStatuses(),
            'filters' => [
                'academic_year_id' => $academicYearId,
                'class_id' => $classId,
                'subject_id' => $subjectId,
                'grade_type' => $gradeType,
                'status' => $status,
            ],
            'teacherAssignments' => $teacherAssignments,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): Response
    {
        $teacher = auth()->user();
        $school = School::first();

        // Get teacher's assignments
        $teacherAssignments = TeacherAssignment::where('teacher_id', $teacher->id)
            ->where('school_id', $school->id)
            ->where('is_active', true)
            ->with(['schoolClass', 'subject', 'academicYear'])
            ->get();

        if ($teacherAssignments->isEmpty()) {
            return redirect()->route('teacher.grades.index')
                ->with('error', 'You are not assigned to any classes yet.');
        }

        // Pre-select values from query parameters
        $selectedClassId = $request->get('class_id');
        $selectedSubjectId = $request->get('subject_id');

        // Get students for the selected class (if any)
        $students = [];
        if ($selectedClassId) {
            $students = StudentEnrollment::where('class_id', $selectedClassId)
                ->where('status', 'enrolled')
                ->with('student')
                ->get()
                ->pluck('student');
        }

        $academicYears = $teacherAssignments->pluck('academicYear')->unique('id')->values();
        $classes = $teacherAssignments->pluck('schoolClass')->unique('id')->values();
        $subjects = $teacherAssignments->pluck('subject')->unique('id')->values();

        return Inertia::render('Teacher/Grades/Create', [
            'academicYears' => $academicYears,
            'classes' => $classes,
            'subjects' => $subjects,
            'students' => $students,
            'gradeTypes' => Grade::getGradeTypes(),
            'statuses' => Grade::getStatuses(),
            'selectedClassId' => $selectedClassId,
            'selectedSubjectId' => $selectedSubjectId,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $teacher = auth()->user();
        $school = School::first();

        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'student_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'subject_id' => 'required|exists:subjects,id',
            'grade_type' => 'required|in:assignment,quiz,exam,project,participation,homework,lab,presentation',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'score' => 'required|numeric|min:0',
            'max_score' => 'required|numeric|min:0.01',
            'assessment_date' => 'required|date',
            'due_date' => 'nullable|date',
            'weight' => 'required|numeric|min:0.01|max:10',
            'status' => 'required|in:draft,published',
            'teacher_notes' => 'nullable|string',
            'student_feedback' => 'nullable|string',
            'is_extra_credit' => 'boolean',
            'is_makeup' => 'boolean',
        ]);

        $validated['school_id'] = $school->id;
        $validated['teacher_id'] = $teacher->id;

        // Verify teacher has assignment for this class and subject
        $teacherAssignment = TeacherAssignment::where('teacher_id', $teacher->id)
            ->where('school_id', $school->id)
            ->where('class_id', $validated['class_id'])
            ->where('subject_id', $validated['subject_id'])
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('is_active', true)
            ->first();

        if (!$teacherAssignment) {
            return back()->withErrors(['class_id' => 'You are not assigned to teach this subject in this class.']);
        }

        // Verify student is enrolled in the class
        $studentEnrollment = StudentEnrollment::where('student_id', $validated['student_id'])
            ->where('class_id', $validated['class_id'])
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('status', 'enrolled')
            ->first();

        if (!$studentEnrollment) {
            return back()->withErrors(['student_id' => 'This student is not enrolled in the selected class.']);
        }

        // Check for duplicate grade
        $existingGrade = Grade::where('student_id', $validated['student_id'])
            ->where('class_id', $validated['class_id'])
            ->where('subject_id', $validated['subject_id'])
            ->where('title', $validated['title'])
            ->where('assessment_date', $validated['assessment_date'])
            ->first();

        if ($existingGrade) {
            return back()->withErrors(['title' => 'A grade with this title already exists for this student on this date.']);
        }

        // Validate score doesn't exceed max_score
        if ($validated['score'] > $validated['max_score']) {
            return back()->withErrors(['score' => 'Score cannot exceed the maximum score.']);
        }

        Grade::create($validated);

        return redirect()->route('teacher.grades.index')
            ->with('success', 'Grade created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Grade $grade): Response
    {
        $teacher = auth()->user();

        // Verify teacher owns this grade
        if ($grade->teacher_id !== $teacher->id) {
            abort(403, 'You can only view your own grades.');
        }

        $grade->load(['student', 'schoolClass', 'subject', 'academicYear']);

        return Inertia::render('Teacher/Grades/Show', [
            'grade' => $grade,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Grade $grade): Response
    {
        $teacher = auth()->user();

        // Verify teacher owns this grade
        if ($grade->teacher_id !== $teacher->id) {
            abort(403, 'You can only edit your own grades.');
        }

        $grade->load(['student', 'schoolClass', 'subject', 'academicYear']);
        $school = School::first();

        // Get teacher's assignments
        $teacherAssignments = TeacherAssignment::where('teacher_id', $teacher->id)
            ->where('school_id', $school->id)
            ->where('is_active', true)
            ->with(['schoolClass', 'subject', 'academicYear'])
            ->get();

        // Get students for the grade's class
        $students = StudentEnrollment::where('class_id', $grade->class_id)
            ->where('status', 'enrolled')
            ->with('student')
            ->get()
            ->pluck('student');

        $academicYears = $teacherAssignments->pluck('academicYear')->unique('id')->values();
        $classes = $teacherAssignments->pluck('schoolClass')->unique('id')->values();
        $subjects = $teacherAssignments->pluck('subject')->unique('id')->values();

        return Inertia::render('Teacher/Grades/Edit', [
            'grade' => $grade,
            'academicYears' => $academicYears,
            'classes' => $classes,
            'subjects' => $subjects,
            'students' => $students,
            'gradeTypes' => Grade::getGradeTypes(),
            'statuses' => Grade::getStatuses(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Grade $grade)
    {
        $teacher = auth()->user();

        // Verify teacher owns this grade
        if ($grade->teacher_id !== $teacher->id) {
            abort(403, 'You can only edit your own grades.');
        }

        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'student_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'subject_id' => 'required|exists:subjects,id',
            'grade_type' => 'required|in:assignment,quiz,exam,project,participation,homework,lab,presentation',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'score' => 'required|numeric|min:0',
            'max_score' => 'required|numeric|min:0.01',
            'assessment_date' => 'required|date',
            'due_date' => 'nullable|date',
            'weight' => 'required|numeric|min:0.01|max:10',
            'status' => 'required|in:draft,published',
            'teacher_notes' => 'nullable|string',
            'student_feedback' => 'nullable|string',
            'is_extra_credit' => 'boolean',
            'is_makeup' => 'boolean',
        ]);

        // Verify teacher has assignment for this class and subject
        $teacherAssignment = TeacherAssignment::where('teacher_id', $teacher->id)
            ->where('school_id', $grade->school_id)
            ->where('class_id', $validated['class_id'])
            ->where('subject_id', $validated['subject_id'])
            ->where('academic_year_id', $validated['academic_year_id'])
            ->where('is_active', true)
            ->first();

        if (!$teacherAssignment) {
            return back()->withErrors(['class_id' => 'You are not assigned to teach this subject in this class.']);
        }

        // Check for duplicate grade (excluding current grade)
        $existingGrade = Grade::where('student_id', $validated['student_id'])
            ->where('class_id', $validated['class_id'])
            ->where('subject_id', $validated['subject_id'])
            ->where('title', $validated['title'])
            ->where('assessment_date', $validated['assessment_date'])
            ->where('id', '!=', $grade->id)
            ->first();

        if ($existingGrade) {
            return back()->withErrors(['title' => 'A grade with this title already exists for this student on this date.']);
        }

        // Validate score doesn't exceed max_score
        if ($validated['score'] > $validated['max_score']) {
            return back()->withErrors(['score' => 'Score cannot exceed the maximum score.']);
        }

        $grade->update($validated);

        return redirect()->route('teacher.grades.index')
            ->with('success', 'Grade updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Grade $grade)
    {
        $teacher = auth()->user();

        // Verify teacher owns this grade
        if ($grade->teacher_id !== $teacher->id) {
            abort(403, 'You can only delete your own grades.');
        }

        $grade->delete();

        return redirect()->route('teacher.grades.index')
            ->with('success', 'Grade deleted successfully.');
    }

    /**
     * Get students for a specific class (AJAX endpoint).
     */
    public function getStudentsForClass(Request $request)
    {
        $classId = $request->get('class_id');

        if (!$classId) {
            return response()->json([]);
        }

        $students = StudentEnrollment::where('class_id', $classId)
            ->where('status', 'enrolled')
            ->with('student')
            ->get()
            ->pluck('student')
            ->map(function ($student) {
                return [
                    'id' => $student->id,
                    'name' => $student->name,
                    'email' => $student->email,
                ];
            });

        return response()->json($students);
    }
}

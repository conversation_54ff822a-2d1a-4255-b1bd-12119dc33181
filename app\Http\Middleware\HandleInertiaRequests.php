<?php

namespace App\Http\Middleware;

use App\Models\AcademicYear;
use Illuminate\Foundation\Inspiring;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        [$message, $author] = str(Inspiring::quotes()->random())->explode('-');

        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'quote' => ['message' => trim($message), 'author' => trim($author)],
            'auth' => [
                'user' => $request->user(),
            ],
            'ziggy' => fn (): array => [
                ...(new Ziggy)->toArray(),
                'location' => $request->url(),
            ],
            'sidebarOpen' => ! $request->hasCookie('sidebar_state') || $request->cookie('sidebar_state') === 'true',
            'locale' => App::getLocale(),
            'translations' => $this->getTranslations(),
            'academicYears' => $this->getAcademicYears($request),
            'currentAcademicYear' => $this->getCurrentAcademicYear($request),
        ];
    }

    /**
     * Get translations for the current locale.
     */
    private function getTranslations(): array
    {
        $locale = App::getLocale();
        $translations = [];

        // Load common translations
        $commonPath = lang_path("{$locale}/common.php");
        if (File::exists($commonPath)) {
            $translations['common'] = include $commonPath;
        }

        // Load school translations
        $schoolPath = lang_path("{$locale}/school.php");
        if (File::exists($schoolPath)) {
            $translations['school'] = include $schoolPath;
        }

        return $translations;
    }

    /**
     * Get academic years for the current user's school.
     */
    private function getAcademicYears(Request $request): array
    {
        // Only provide academic years for authenticated users
        if (!$request->user()) {
            return [];
        }

        // Get academic years for the user's school
        $schoolId = $request->user()->school_id;

        if (!$schoolId) {
            return [];
        }

        return AcademicYear::where('school_id', $schoolId)
            ->orderBy('start_date', 'desc')
            ->get()
            ->map(function ($academicYear) {
                return [
                    'id' => $academicYear->id,
                    'name' => $academicYear->name,
                    'localized_name' => $academicYear->localized_name,
                    'start_date' => $academicYear->start_date->toDateString(),
                    'end_date' => $academicYear->end_date->toDateString(),
                    'is_current' => $academicYear->is_current,
                    'is_active' => $academicYear->is_active,
                ];
            })
            ->toArray();
    }

    /**
     * Get the current academic year for the user's school.
     */
    private function getCurrentAcademicYear(Request $request): ?array
    {
        // Only provide current academic year for authenticated users
        if (!$request->user()) {
            return null;
        }

        // Get current academic year for the user's school
        $schoolId = $request->user()->school_id;

        if (!$schoolId) {
            return null;
        }

        $currentAcademicYear = AcademicYear::where('school_id', $schoolId)
            ->where('is_current', true)
            ->first();

        if (!$currentAcademicYear) {
            return null;
        }

        return [
            'id' => $currentAcademicYear->id,
            'name' => $currentAcademicYear->name,
            'localized_name' => $currentAcademicYear->localized_name,
            'start_date' => $currentAcademicYear->start_date->toDateString(),
            'end_date' => $currentAcademicYear->end_date->toDateString(),
            'is_current' => $currentAcademicYear->is_current,
            'is_active' => $currentAcademicYear->is_active,
        ];
    }
}

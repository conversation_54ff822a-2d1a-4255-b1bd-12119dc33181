<?php

namespace App\Http\Middleware;

use App\Models\School;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get supported languages from school settings or default
        $school = School::first();
        $supportedLanguages = $school?->supported_languages ?? ['en', 'ar', 'fr'];

        // Check if locale is provided in URL parameter
        if ($request->has('locale') && in_array($request->get('locale'), $supportedLanguages)) {
            $locale = $request->get('locale');
            Session::put('locale', $locale);
        }
        // Check if locale is stored in session
        elseif (Session::has('locale') && in_array(Session::get('locale'), $supportedLanguages)) {
            $locale = Session::get('locale');
        }
        // Check if user has a preferred language (if authenticated)
        elseif ($request->user() && isset($request->user()->preferred_language) && in_array($request->user()->preferred_language, $supportedLanguages)) {
            $locale = $request->user()->preferred_language;
            Session::put('locale', $locale);
        }
        // Use school's default language
        elseif ($school && in_array($school->default_language, $supportedLanguages)) {
            $locale = $school->default_language;
        }
        // Fallback to application default
        else {
            $locale = config('app.locale', 'en');
        }

        // Set the application locale
        App::setLocale($locale);

        // Set the direction for RTL languages
        $rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        $direction = in_array($locale, $rtlLanguages) ? 'rtl' : 'ltr';

        // Share locale and direction with all views
        view()->share('currentLocale', $locale);
        view()->share('textDirection', $direction);
        view()->share('supportedLanguages', $supportedLanguages);

        return $next($request);
    }
}

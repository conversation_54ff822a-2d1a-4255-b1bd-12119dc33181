<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Classroom extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'name',
        'name_ar',
        'name_fr',
        'description',
        'description_ar',
        'description_fr',
        'room_number',
        'building',
        'floor',
        'capacity',
        'classroom_type',
        'features',
        'subjects_suitable',
        'is_active',
        'is_available',
        'maintenance_notes',
        'last_maintenance',
        'location_details',
        'accessibility_features',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'features' => 'array',
        'subjects_suitable' => 'array',
        'is_active' => 'boolean',
        'is_available' => 'boolean',
        'last_maintenance' => 'date',
        'accessibility_features' => 'array',
        'settings' => 'array',
    ];

    /**
     * Get the school that owns the classroom.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the classroom schedules for this classroom.
     */
    public function classroomSchedules(): HasMany
    {
        return $this->hasMany(ClassroomSchedule::class);
    }

    /**
     * Get the localized name based on current locale.
     */
    protected function localizedName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->name_ar ?? $this->name,
                    'fr' => $this->name_fr ?? $this->name,
                    default => $this->name,
                };
            }
        );
    }

    /**
     * Get the localized description based on current locale.
     */
    protected function localizedDescription(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->description_ar ?? $this->description,
                    'fr' => $this->description_fr ?? $this->description,
                    default => $this->description,
                };
            }
        );
    }

    /**
     * Get the classroom type label.
     */
    protected function classroomTypeLabel(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match ($this->classroom_type) {
                    'regular' => 'Regular Classroom',
                    'laboratory' => 'Laboratory',
                    'computer_lab' => 'Computer Lab',
                    'library' => 'Library',
                    'gymnasium' => 'Gymnasium',
                    'auditorium' => 'Auditorium',
                    'art_room' => 'Art Room',
                    'music_room' => 'Music Room',
                    'workshop' => 'Workshop',
                    default => ucfirst(str_replace('_', ' ', $this->classroom_type)),
                };
            }
        );
    }

    /**
     * Get the full location string.
     */
    protected function fullLocation(): Attribute
    {
        return Attribute::make(
            get: function () {
                $parts = array_filter([
                    $this->building ? "Building {$this->building}" : null,
                    $this->floor ? "Floor {$this->floor}" : null,
                    $this->room_number ? "Room {$this->room_number}" : null,
                ]);

                return implode(', ', $parts) ?: $this->name;
            }
        );
    }

    /**
     * Scope to get active classrooms.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get available classrooms.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to get classrooms for a specific school.
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get classrooms by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('classroom_type', $type);
    }

    /**
     * Scope to get classrooms suitable for a subject.
     */
    public function scopeSuitableForSubject($query, $subjectId)
    {
        return $query->whereJsonContains('subjects_suitable', $subjectId);
    }

    /**
     * Scope to get classrooms in a specific building.
     */
    public function scopeInBuilding($query, $building)
    {
        return $query->where('building', $building);
    }

    /**
     * Scope to get classrooms on a specific floor.
     */
    public function scopeOnFloor($query, $floor)
    {
        return $query->where('floor', $floor);
    }

    /**
     * Check if classroom is suitable for a subject.
     */
    public function isSuitableForSubject($subjectId): bool
    {
        if (empty($this->subjects_suitable)) {
            return true; // If no restrictions, suitable for all subjects
        }

        return in_array($subjectId, $this->subjects_suitable);
    }

    /**
     * Check if classroom has a specific feature.
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    /**
     * Get all available classroom types.
     */
    public static function getClassroomTypes(): array
    {
        return [
            'regular' => 'Regular Classroom',
            'laboratory' => 'Laboratory',
            'computer_lab' => 'Computer Lab',
            'library' => 'Library',
            'gymnasium' => 'Gymnasium',
            'auditorium' => 'Auditorium',
            'art_room' => 'Art Room',
            'music_room' => 'Music Room',
            'workshop' => 'Workshop',
        ];
    }

    /**
     * Get all available features.
     */
    public static function getAvailableFeatures(): array
    {
        return [
            'projector' => 'Projector',
            'smartboard' => 'Smart Board',
            'computers' => 'Computers',
            'wifi' => 'WiFi',
            'air_conditioning' => 'Air Conditioning',
            'sound_system' => 'Sound System',
            'laboratory_equipment' => 'Laboratory Equipment',
            'art_supplies' => 'Art Supplies',
            'musical_instruments' => 'Musical Instruments',
            'sports_equipment' => 'Sports Equipment',
            'wheelchair_accessible' => 'Wheelchair Accessible',
            'emergency_exit' => 'Emergency Exit',
        ];
    }

    /**
     * Get classroom utilization statistics.
     */
    public function getUtilizationStats($academicYearId = null): array
    {
        $query = $this->classroomSchedules();

        if ($academicYearId) {
            $query->where('academic_year_id', $academicYearId);
        }

        $schedules = $query->get();

        return [
            'total_sessions' => $schedules->count(),
            'unique_classes' => $schedules->pluck('class_id')->unique()->count(),
            'unique_subjects' => $schedules->pluck('subject_id')->unique()->count(),
            'utilization_percentage' => $this->calculateUtilizationPercentage($schedules),
        ];
    }

    /**
     * Calculate utilization percentage based on schedules.
     */
    private function calculateUtilizationPercentage($schedules): float
    {
        // This is a simplified calculation
        // In a real system, you'd calculate based on time slots and availability
        $totalPossibleSlots = 40; // Example: 8 periods × 5 days
        $usedSlots = $schedules->count();

        return $totalPossibleSlots > 0 ? round(($usedSlots / $totalPossibleSlots) * 100, 2) : 0;
    }
}

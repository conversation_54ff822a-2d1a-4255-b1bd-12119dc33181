<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClassroomSchedule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'academic_year_id',
        'classroom_id',
        'class_id',
        'subject_id',
        'teacher_id',
        'day_of_week',
        'start_time',
        'end_time',
        'period_number',
        'effective_from',
        'effective_until',
        'schedule_type',
        'is_active',
        'is_recurring',
        'notes',
        'exceptions',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'effective_from' => 'date',
        'effective_until' => 'date',
        'is_active' => 'boolean',
        'is_recurring' => 'boolean',
        'exceptions' => 'array',
        'settings' => 'array',
    ];

    /**
     * Get the school that owns the schedule.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the academic year for the schedule.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the classroom for the schedule.
     */
    public function classroom(): BelongsTo
    {
        return $this->belongsTo(Classroom::class);
    }

    /**
     * Get the class for the schedule.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the subject for the schedule.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the teacher for the schedule.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Get the day of week label.
     */
    protected function dayOfWeekLabel(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match ($this->day_of_week) {
                    'monday' => 'Monday',
                    'tuesday' => 'Tuesday',
                    'wednesday' => 'Wednesday',
                    'thursday' => 'Thursday',
                    'friday' => 'Friday',
                    'saturday' => 'Saturday',
                    'sunday' => 'Sunday',
                    default => ucfirst($this->day_of_week),
                };
            }
        );
    }

    /**
     * Get the schedule type label.
     */
    protected function scheduleTypeLabel(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match ($this->schedule_type) {
                    'regular' => 'Regular Class',
                    'exam' => 'Examination',
                    'makeup' => 'Makeup Class',
                    'special' => 'Special Event',
                    default => ucfirst($this->schedule_type),
                };
            }
        );
    }

    /**
     * Get the time range string.
     */
    protected function timeRange(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
            }
        );
    }

    /**
     * Get the duration in minutes.
     */
    protected function durationMinutes(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->start_time->diffInMinutes($this->end_time);
            }
        );
    }

    /**
     * Scope to get active schedules.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get schedules for a specific school.
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get schedules for a specific academic year.
     */
    public function scopeForAcademicYear($query, $academicYearId)
    {
        return $query->where('academic_year_id', $academicYearId);
    }

    /**
     * Scope to get schedules for a specific classroom.
     */
    public function scopeForClassroom($query, $classroomId)
    {
        return $query->where('classroom_id', $classroomId);
    }

    /**
     * Scope to get schedules for a specific class.
     */
    public function scopeForClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to get schedules for a specific teacher.
     */
    public function scopeForTeacher($query, $teacherId)
    {
        return $query->where('teacher_id', $teacherId);
    }

    /**
     * Scope to get schedules for a specific day.
     */
    public function scopeForDay($query, $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    /**
     * Scope to get schedules by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('schedule_type', $type);
    }

    /**
     * Scope to get current schedules (within effective date range).
     */
    public function scopeCurrent($query)
    {
        $today = now()->toDateString();
        return $query->where('effective_from', '<=', $today)
            ->where('effective_until', '>=', $today);
    }

    /**
     * Check if the schedule is currently effective.
     */
    public function isCurrentlyEffective(): bool
    {
        $today = now()->toDateString();
        return $this->effective_from <= $today && $this->effective_until >= $today;
    }

    /**
     * Check if a specific date is an exception.
     */
    public function isExceptionDate(string $date): bool
    {
        return in_array($date, $this->exceptions ?? []);
    }

    /**
     * Get all available days of week.
     */
    public static function getDaysOfWeek(): array
    {
        return [
            'monday' => 'Monday',
            'tuesday' => 'Tuesday',
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
            'saturday' => 'Saturday',
            'sunday' => 'Sunday',
        ];
    }

    /**
     * Get all available schedule types.
     */
    public static function getScheduleTypes(): array
    {
        return [
            'regular' => 'Regular Class',
            'exam' => 'Examination',
            'makeup' => 'Makeup Class',
            'special' => 'Special Event',
        ];
    }

    /**
     * Check for scheduling conflicts.
     */
    public static function hasConflict($classroomId, $dayOfWeek, $startTime, $endTime, $effectiveFrom, $excludeId = null): bool
    {
        $query = static::where('classroom_id', $classroomId)
            ->where('day_of_week', $dayOfWeek)
            ->where('is_active', true)
            ->where(function ($q) use ($effectiveFrom) {
                $q->where('effective_from', '<=', $effectiveFrom)
                  ->where('effective_until', '>=', $effectiveFrom);
            })
            ->where(function ($q) use ($startTime, $endTime) {
                $q->whereBetween('start_time', [$startTime, $endTime])
                  ->orWhereBetween('end_time', [$startTime, $endTime])
                  ->orWhere(function ($subQ) use ($startTime, $endTime) {
                      $subQ->where('start_time', '<=', $startTime)
                           ->where('end_time', '>=', $endTime);
                  });
            });

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }
}

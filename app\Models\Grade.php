<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Grade extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'academic_year_id',
        'student_id',
        'teacher_id',
        'class_id',
        'subject_id',
        'grading_system_id',
        'grade_type',
        'title',
        'description',
        'score',
        'max_score',
        'percentage',
        'letter_grade',
        'gpa_points',
        'assessment_date',
        'due_date',
        'graded_date',
        'weight',
        'status',
        'teacher_notes',
        'student_feedback',
        'is_extra_credit',
        'is_makeup',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'score' => 'decimal:2',
        'max_score' => 'decimal:2',
        'percentage' => 'decimal:2',
        'gpa_points' => 'decimal:2',
        'weight' => 'decimal:2',
        'assessment_date' => 'date',
        'due_date' => 'date',
        'graded_date' => 'date',
        'is_extra_credit' => 'boolean',
        'is_makeup' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Get the school that owns the grade.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the academic year for the grade.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the student for the grade.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    /**
     * Get the teacher for the grade.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Get the class for the grade.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the subject for the grade.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the grading system for the grade.
     */
    public function gradingSystem(): BelongsTo
    {
        return $this->belongsTo(GradingSystem::class);
    }

    /**
     * Get the grade display name.
     */
    protected function displayName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $student = $this->student?->name ?? 'Unknown Student';
                $subject = $this->subject?->name ?? 'Unknown Subject';

                return "{$this->title} - {$student} ({$subject})";
            }
        );
    }

    /**
     * Get the grade status label.
     */
    protected function statusLabel(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match ($this->status) {
                    'draft' => 'Draft',
                    'published' => 'Published',
                    'archived' => 'Archived',
                    default => 'Unknown',
                };
            }
        );
    }

    /**
     * Get the grade type label.
     */
    protected function gradeTypeLabel(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match ($this->grade_type) {
                    'assignment' => 'Assignment',
                    'quiz' => 'Quiz',
                    'exam' => 'Exam',
                    'project' => 'Project',
                    'participation' => 'Participation',
                    'homework' => 'Homework',
                    'lab' => 'Lab Work',
                    'presentation' => 'Presentation',
                    default => ucfirst($this->grade_type),
                };
            }
        );
    }

    /**
     * Check if the grade is published.
     */
    protected function isPublished(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status === 'published'
        );
    }

    /**
     * Check if the grade is overdue for grading.
     */
    protected function isOverdue(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->status === 'published' || !$this->due_date) {
                    return false;
                }

                return $this->due_date < now() && !$this->graded_date;
            }
        );
    }

    /**
     * Scope to get published grades.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope to get draft grades.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope to get grades for a specific school.
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get grades for a specific academic year.
     */
    public function scopeForAcademicYear($query, $academicYearId)
    {
        return $query->where('academic_year_id', $academicYearId);
    }

    /**
     * Scope to get grades for a specific student.
     */
    public function scopeForStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    /**
     * Scope to get grades for a specific teacher.
     */
    public function scopeForTeacher($query, $teacherId)
    {
        return $query->where('teacher_id', $teacherId);
    }

    /**
     * Scope to get grades for a specific class.
     */
    public function scopeForClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to get grades for a specific subject.
     */
    public function scopeForSubject($query, $subjectId)
    {
        return $query->where('subject_id', $subjectId);
    }

    /**
     * Scope to get grades by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('grade_type', $type);
    }

    /**
     * Scope to get extra credit grades.
     */
    public function scopeExtraCredit($query)
    {
        return $query->where('is_extra_credit', true);
    }

    /**
     * Scope to get makeup grades.
     */
    public function scopeMakeup($query)
    {
        return $query->where('is_makeup', true);
    }

    /**
     * Get all available grade types.
     */
    public static function getGradeTypes(): array
    {
        return [
            'assignment' => 'Assignment',
            'quiz' => 'Quiz',
            'exam' => 'Exam',
            'project' => 'Project',
            'participation' => 'Participation',
            'homework' => 'Homework',
            'lab' => 'Lab Work',
            'presentation' => 'Presentation',
        ];
    }

    /**
     * Get all available statuses.
     */
    public static function getStatuses(): array
    {
        return [
            'draft' => 'Draft',
            'published' => 'Published',
            'archived' => 'Archived',
        ];
    }

    /**
     * Calculate percentage from score and max_score.
     */
    public function calculatePercentage(): float
    {
        if ($this->max_score <= 0) {
            return 0;
        }

        return round(($this->score / $this->max_score) * 100, 2);
    }

    /**
     * Calculate grade using the assigned grading system.
     */
    public function calculateGradeWithSystem(): array
    {
        if (!$this->gradingSystem) {
            // Fallback to legacy calculation if no grading system assigned
            return [
                'score' => $this->score,
                'grade' => $this->calculateLetterGrade(),
                'percentage' => $this->calculatePercentage(),
                'is_passing' => $this->calculatePercentage() >= 60,
            ];
        }

        return $this->gradingSystem->convertScore($this->score);
    }

    /**
     * Calculate letter grade from percentage (legacy method).
     */
    public function calculateLetterGrade(): string
    {
        if ($this->gradingSystem) {
            $gradeData = $this->gradingSystem->convertScore($this->score);
            return $gradeData['grade'] ?? 'N/A';
        }

        // Legacy calculation for backward compatibility
        $percentage = $this->percentage ?? $this->calculatePercentage();

        return match (true) {
            $percentage >= 97 => 'A+',
            $percentage >= 93 => 'A',
            $percentage >= 90 => 'A-',
            $percentage >= 87 => 'B+',
            $percentage >= 83 => 'B',
            $percentage >= 80 => 'B-',
            $percentage >= 77 => 'C+',
            $percentage >= 73 => 'C',
            $percentage >= 70 => 'C-',
            $percentage >= 67 => 'D+',
            $percentage >= 63 => 'D',
            $percentage >= 60 => 'D-',
            default => 'F',
        };
    }

    /**
     * Calculate GPA points from letter grade (legacy method).
     */
    public function calculateGpaPoints(): float
    {
        if ($this->gradingSystem) {
            $gradeData = $this->gradingSystem->convertScore($this->score);
            return $gradeData['gpa_points'] ?? 0.0;
        }

        // Legacy calculation for backward compatibility
        $letterGrade = $this->letter_grade ?? $this->calculateLetterGrade();

        return match ($letterGrade) {
            'A+', 'A' => 4.0,
            'A-' => 3.7,
            'B+' => 3.3,
            'B' => 3.0,
            'B-' => 2.7,
            'C+' => 2.3,
            'C' => 2.0,
            'C-' => 1.7,
            'D+' => 1.3,
            'D' => 1.0,
            'D-' => 0.7,
            default => 0.0,
        };
    }

    /**
     * Check if the grade is passing based on grading system.
     */
    public function isPassing(): bool
    {
        if ($this->gradingSystem) {
            return $this->score >= $this->gradingSystem->passing_score;
        }

        // Legacy calculation
        return $this->calculatePercentage() >= 60;
    }

    /**
     * Auto-calculate derived fields before saving.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($grade) {
            // Auto-assign grading system if not set
            if (is_null($grade->grading_system_id) && $grade->schoolClass) {
                $gradingSystem = GradingSystem::where('school_id', $grade->school_id)
                    ->where('is_active', true)
                    ->whereJsonContains('applicable_grade_levels', $grade->schoolClass->grade_level)
                    ->where('is_default', true)
                    ->first();

                if ($gradingSystem) {
                    $grade->grading_system_id = $gradingSystem->id;
                }
            }

            // Auto-calculate percentage if not set
            if (is_null($grade->percentage)) {
                $grade->percentage = $grade->calculatePercentage();
            }

            // Auto-calculate letter grade and GPA using grading system
            if ($grade->gradingSystem) {
                $gradeData = $grade->gradingSystem->convertScore($grade->score);

                if (is_null($grade->letter_grade)) {
                    $grade->letter_grade = $gradeData['grade'] ?? null;
                }

                if (is_null($grade->gpa_points)) {
                    $grade->gpa_points = $gradeData['gpa_points'] ?? null;
                }
            } else {
                // Fallback to legacy calculation
                if (is_null($grade->letter_grade)) {
                    $grade->letter_grade = $grade->calculateLetterGrade();
                }

                if (is_null($grade->gpa_points)) {
                    $grade->gpa_points = $grade->calculateGpaPoints();
                }
            }

            // Set graded_date when status changes to published
            if ($grade->status === 'published' && is_null($grade->graded_date)) {
                $grade->graded_date = now();
            }
        });
    }

    /**
     * Get grade statistics for a student in a subject.
     */
    public static function getStudentSubjectStats($studentId, $subjectId, $academicYearId = null)
    {
        $query = static::where('student_id', $studentId)
            ->where('subject_id', $subjectId)
            ->where('status', 'published');

        if ($academicYearId) {
            $query->where('academic_year_id', $academicYearId);
        }

        $grades = $query->get();

        if ($grades->isEmpty()) {
            return null;
        }

        $totalWeightedScore = 0;
        $totalWeight = 0;

        foreach ($grades as $grade) {
            $weightedScore = $grade->percentage * $grade->weight;
            $totalWeightedScore += $weightedScore;
            $totalWeight += $grade->weight;
        }

        $averagePercentage = $totalWeight > 0 ? $totalWeightedScore / $totalWeight : 0;

        return [
            'count' => $grades->count(),
            'average_percentage' => round($averagePercentage, 2),
            'letter_grade' => static::percentageToLetterGrade($averagePercentage),
            'gpa_points' => static::percentageToGpaPoints($averagePercentage),
            'highest' => $grades->max('percentage'),
            'lowest' => $grades->min('percentage'),
        ];
    }

    /**
     * Convert percentage to letter grade (static method).
     */
    public static function percentageToLetterGrade(float $percentage): string
    {
        return match (true) {
            $percentage >= 97 => 'A+',
            $percentage >= 93 => 'A',
            $percentage >= 90 => 'A-',
            $percentage >= 87 => 'B+',
            $percentage >= 83 => 'B',
            $percentage >= 80 => 'B-',
            $percentage >= 77 => 'C+',
            $percentage >= 73 => 'C',
            $percentage >= 70 => 'C-',
            $percentage >= 67 => 'D+',
            $percentage >= 63 => 'D',
            $percentage >= 60 => 'D-',
            default => 'F',
        };
    }

    /**
     * Convert percentage to GPA points (static method).
     */
    public static function percentageToGpaPoints(float $percentage): float
    {
        $letterGrade = static::percentageToLetterGrade($percentage);

        return match ($letterGrade) {
            'A+', 'A' => 4.0,
            'A-' => 3.7,
            'B+' => 3.3,
            'B' => 3.0,
            'B-' => 2.7,
            'C+' => 2.3,
            'C' => 2.0,
            'C-' => 1.7,
            'D+' => 1.3,
            'D' => 1.0,
            'D-' => 0.7,
            default => 0.0,
        };
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GradingSystem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'name',
        'name_ar',
        'name_fr',
        'description',
        'description_ar',
        'description_fr',
        'system_type',
        'min_score',
        'max_score',
        'passing_score',
        'applicable_grade_levels',
        'grade_scale_mapping',
        'supports_trimesters',
        'supports_semesters',
        'assessment_types',
        'is_active',
        'is_default',
        'effective_from',
        'effective_until',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'min_score' => 'decimal:2',
        'max_score' => 'decimal:2',
        'passing_score' => 'decimal:2',
        'applicable_grade_levels' => 'array',
        'grade_scale_mapping' => 'array',
        'supports_trimesters' => 'boolean',
        'supports_semesters' => 'boolean',
        'assessment_types' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'effective_from' => 'date',
        'effective_until' => 'date',
        'settings' => 'array',
    ];

    /**
     * Get the school that owns the grading system.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the grades using this grading system.
     */
    public function grades(): HasMany
    {
        return $this->hasMany(Grade::class);
    }

    /**
     * Get the localized name based on current locale.
     */
    protected function localizedName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->name_ar ?? $this->name,
                    'fr' => $this->name_fr ?? $this->name,
                    default => $this->name,
                };
            }
        );
    }

    /**
     * Get the localized description based on current locale.
     */
    protected function localizedDescription(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->description_ar ?? $this->description,
                    'fr' => $this->description_fr ?? $this->description,
                    default => $this->description,
                };
            }
        );
    }

    /**
     * Scope to get active grading systems.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get default grading systems.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get grading systems for a specific school.
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get grading systems by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('system_type', $type);
    }

    /**
     * Scope to get grading systems applicable to a grade level.
     */
    public function scopeForGradeLevel($query, $gradeLevel)
    {
        return $query->whereJsonContains('applicable_grade_levels', $gradeLevel);
    }

    /**
     * Check if this grading system is applicable to a grade level.
     */
    public function isApplicableToGradeLevel(string $gradeLevel): bool
    {
        return in_array($gradeLevel, $this->applicable_grade_levels ?? []);
    }

    /**
     * Convert a score to the appropriate grade representation.
     */
    public function convertScore(float $score): array
    {
        // Ensure score is within bounds
        $score = max($this->min_score, min($this->max_score, $score));

        return match ($this->system_type) {
            '20-point' => $this->convertTo20Point($score),
            'letter' => $this->convertToLetter($score),
            '10-point' => $this->convertTo10Point($score),
            'ECTS' => $this->convertToECTS($score),
            'percentage' => $this->convertToPercentage($score),
            default => ['score' => $score, 'grade' => (string) $score],
        };
    }

    /**
     * Convert score to Tunisian 20-point system.
     */
    private function convertTo20Point(float $score): array
    {
        $percentage = ($score / $this->max_score) * 100;

        // Tunisian 20-point scale mapping
        $grade = match (true) {
            $percentage >= 90 => ['score' => $score, 'grade' => 'Excellent', 'points' => $score, 'level' => 'Très Bien'],
            $percentage >= 80 => ['score' => $score, 'grade' => 'Very Good', 'points' => $score, 'level' => 'Très Bien'],
            $percentage >= 70 => ['score' => $score, 'grade' => 'Good', 'points' => $score, 'level' => 'Bien'],
            $percentage >= 60 => ['score' => $score, 'grade' => 'Satisfactory', 'points' => $score, 'level' => 'Assez Bien'],
            $percentage >= 50 => ['score' => $score, 'grade' => 'Minimum Passing', 'points' => $score, 'level' => 'Passable'],
            $percentage >= 45 => ['score' => $score, 'grade' => 'Conditional Pass', 'points' => $score, 'level' => 'Conditionnelle'],
            default => ['score' => $score, 'grade' => 'Insufficient', 'points' => $score, 'level' => 'Insuffisant'],
        };

        $grade['percentage'] = round($percentage, 2);
        $grade['is_passing'] = $score >= $this->passing_score;

        return $grade;
    }

    /**
     * Convert score to letter grade system (Primary education).
     */
    private function convertToLetter(float $score): array
    {
        $percentage = ($score / $this->max_score) * 100;

        // Tunisian primary education letter system
        $grade = match (true) {
            $percentage >= 85 => ['score' => $score, 'grade' => 'E', 'level' => 'Exceeds', 'level_ar' => 'متفوق', 'level_fr' => 'Dépasse'],
            $percentage >= 70 => ['score' => $score, 'grade' => 'M', 'level' => 'Meets', 'level_ar' => 'يحقق', 'level_fr' => 'Atteint'],
            $percentage >= 55 => ['score' => $score, 'grade' => 'S', 'level' => 'Steady', 'level_ar' => 'مستقر', 'level_fr' => 'Stable'],
            default => ['score' => $score, 'grade' => 'L', 'level' => 'Limited', 'level_ar' => 'محدود', 'level_fr' => 'Limité'],
        };

        $grade['percentage'] = round($percentage, 2);
        $grade['is_passing'] = $percentage >= 55; // Steady progress is considered passing

        return $grade;
    }

    /**
     * Convert score to 10-point system (First trimester primary).
     */
    private function convertTo10Point(float $score): array
    {
        $percentage = ($score / $this->max_score) * 100;
        $tenPointScore = ($score / $this->max_score) * 10;

        $grade = [
            'score' => $score,
            'grade' => round($tenPointScore, 2),
            'points' => round($tenPointScore, 2),
            'percentage' => round($percentage, 2),
            'is_passing' => $tenPointScore >= 5,
        ];

        return $grade;
    }

    /**
     * Convert score to ECTS system.
     */
    private function convertToECTS(float $score): array
    {
        $percentage = ($score / $this->max_score) * 100;

        // ECTS grade mapping for Tunisian context
        $grade = match (true) {
            $percentage >= 90 => ['score' => $score, 'grade' => 'A', 'level' => 'Excellent'],
            $percentage >= 80 => ['score' => $score, 'grade' => 'B', 'level' => 'Very Good'],
            $percentage >= 70 => ['score' => $score, 'grade' => 'C', 'level' => 'Good'],
            $percentage >= 60 => ['score' => $score, 'grade' => 'D', 'level' => 'Satisfactory'],
            $percentage >= 50 => ['score' => $score, 'grade' => 'E', 'level' => 'Sufficient'],
            default => ['score' => $score, 'grade' => 'F', 'level' => 'Fail'],
        };

        $grade['percentage'] = round($percentage, 2);
        $grade['is_passing'] = $percentage >= 50;

        return $grade;
    }

    /**
     * Convert score to percentage system.
     */
    private function convertToPercentage(float $score): array
    {
        $percentage = ($score / $this->max_score) * 100;

        return [
            'score' => $score,
            'grade' => round($percentage, 2) . '%',
            'percentage' => round($percentage, 2),
            'is_passing' => $percentage >= (($this->passing_score / $this->max_score) * 100),
        ];
    }

    /**
     * Get all available system types.
     */
    public static function getSystemTypes(): array
    {
        return [
            'letter' => 'Letter Grade (Primary)',
            '20-point' => 'Tunisian 20-Point Scale',
            '10-point' => '10-Point Scale',
            'ECTS' => 'ECTS European Scale',
            'percentage' => 'Percentage Scale',
        ];
    }

    /**
     * Create default Tunisian grading systems for a school.
     */
    public static function createDefaultSystems(School $school): void
    {
        // Primary Education Letter System (Kindergarten - Grade 2)
        static::create([
            'school_id' => $school->id,
            'name' => 'Primary Letter Grade System',
            'name_ar' => 'نظام الدرجات الحرفية للمرحلة الابتدائية',
            'name_fr' => 'Système de Notes par Lettres Primaire',
            'description' => 'Letter-based grading system for early primary education',
            'description_ar' => 'نظام التقييم بالحروف للمرحلة الابتدائية المبكرة',
            'description_fr' => 'Système d\'évaluation par lettres pour l\'enseignement primaire précoce',
            'system_type' => 'letter',
            'min_score' => 0,
            'max_score' => 4,
            'passing_score' => 2, // S level
            'applicable_grade_levels' => ['Pre-K', 'Kindergarten', 'Grade 1', 'Grade 2'],
            'grade_scale_mapping' => [
                'E' => ['min' => 3.4, 'max' => 4.0, 'level' => 'Exceeds'],
                'M' => ['min' => 2.8, 'max' => 3.39, 'level' => 'Meets'],
                'S' => ['min' => 2.2, 'max' => 2.79, 'level' => 'Steady'],
                'L' => ['min' => 0, 'max' => 2.19, 'level' => 'Limited'],
            ],
            'supports_trimesters' => true,
            'supports_semesters' => false,
            'assessment_types' => ['observation', 'activity', 'project', 'participation'],
            'is_active' => true,
            'is_default' => true,
            'effective_from' => now(),
        ]);

        // 10-Point System (First trimester, Grades 3-5)
        static::create([
            'school_id' => $school->id,
            'name' => '10-Point Scale (First Trimester)',
            'name_ar' => 'نظام العشر نقاط (الفصل الأول)',
            'name_fr' => 'Échelle de 10 Points (Premier Trimestre)',
            'description' => '10-point grading system for first trimester of intermediate primary',
            'description_ar' => 'نظام التقييم بعشر نقاط للفصل الأول من المرحلة الابتدائية المتوسطة',
            'description_fr' => 'Système d\'évaluation sur 10 points pour le premier trimestre du primaire intermédiaire',
            'system_type' => '10-point',
            'min_score' => 0,
            'max_score' => 10,
            'passing_score' => 5,
            'applicable_grade_levels' => ['Grade 3', 'Grade 4', 'Grade 5'],
            'grade_scale_mapping' => [
                'Excellent' => ['min' => 9, 'max' => 10],
                'Very Good' => ['min' => 8, 'max' => 8.99],
                'Good' => ['min' => 7, 'max' => 7.99],
                'Satisfactory' => ['min' => 6, 'max' => 6.99],
                'Passing' => ['min' => 5, 'max' => 5.99],
                'Insufficient' => ['min' => 0, 'max' => 4.99],
            ],
            'supports_trimesters' => true,
            'supports_semesters' => false,
            'assessment_types' => ['assignment', 'quiz', 'exam', 'project', 'participation'],
            'is_active' => true,
            'is_default' => false,
            'effective_from' => now(),
        ]);

        // 20-Point System (Secondary and Higher Education)
        static::create([
            'school_id' => $school->id,
            'name' => 'Tunisian 20-Point Scale',
            'name_ar' => 'نظام العشرين نقطة التونسي',
            'name_fr' => 'Échelle Tunisienne de 20 Points',
            'description' => 'Traditional Tunisian 20-point grading system for secondary and higher education',
            'description_ar' => 'نظام التقييم التونسي التقليدي بعشرين نقطة للتعليم الثانوي والعالي',
            'description_fr' => 'Système d\'évaluation tunisien traditionnel sur 20 points pour l\'enseignement secondaire et supérieur',
            'system_type' => '20-point',
            'min_score' => 0,
            'max_score' => 20,
            'passing_score' => 10,
            'applicable_grade_levels' => ['Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'],
            'grade_scale_mapping' => [
                'Excellent' => ['min' => 18, 'max' => 20, 'level' => 'Très Bien'],
                'Very Good' => ['min' => 16, 'max' => 17.99, 'level' => 'Très Bien'],
                'Good' => ['min' => 14, 'max' => 15.99, 'level' => 'Bien'],
                'Satisfactory' => ['min' => 12, 'max' => 13.99, 'level' => 'Assez Bien'],
                'Minimum Passing' => ['min' => 10, 'max' => 11.99, 'level' => 'Passable'],
                'Conditional Pass' => ['min' => 9, 'max' => 9.99, 'level' => 'Conditionnelle'],
                'Insufficient' => ['min' => 0, 'max' => 8.99, 'level' => 'Insuffisant'],
            ],
            'supports_trimesters' => true,
            'supports_semesters' => true,
            'assessment_types' => ['assignment', 'quiz', 'exam', 'project', 'participation', 'homework', 'lab', 'presentation'],
            'is_active' => true,
            'is_default' => true,
            'effective_from' => now(),
        ]);

        // ECTS System (University preparation)
        static::create([
            'school_id' => $school->id,
            'name' => 'ECTS European Scale',
            'name_ar' => 'نظام ECTS الأوروبي',
            'name_fr' => 'Échelle Européenne ECTS',
            'description' => 'European Credit Transfer System for international compatibility',
            'description_ar' => 'نظام التحويل الأوروبي للتوافق الدولي',
            'description_fr' => 'Système européen de transfert de crédits pour la compatibilité internationale',
            'system_type' => 'ECTS',
            'min_score' => 0,
            'max_score' => 100,
            'passing_score' => 50,
            'applicable_grade_levels' => ['Grade 11', 'Grade 12'],
            'grade_scale_mapping' => [
                'A' => ['min' => 90, 'max' => 100, 'level' => 'Excellent'],
                'B' => ['min' => 80, 'max' => 89, 'level' => 'Very Good'],
                'C' => ['min' => 70, 'max' => 79, 'level' => 'Good'],
                'D' => ['min' => 60, 'max' => 69, 'level' => 'Satisfactory'],
                'E' => ['min' => 50, 'max' => 59, 'level' => 'Sufficient'],
                'F' => ['min' => 0, 'max' => 49, 'level' => 'Fail'],
            ],
            'supports_trimesters' => true,
            'supports_semesters' => true,
            'assessment_types' => ['assignment', 'quiz', 'exam', 'project', 'participation', 'homework', 'lab', 'presentation'],
            'is_active' => true,
            'is_default' => false,
            'effective_from' => now(),
        ]);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Period extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'academic_year_id',
        'name',
        'name_ar',
        'name_fr',
        'description',
        'description_ar',
        'description_fr',
        'order',
        'type',
        'start_date',
        'end_date',
        'is_active',
        'is_current',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
        'is_current' => 'boolean',
        'settings' => 'array',
        'order' => 'integer',
    ];

    /**
     * Get the academic year that owns the period.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the localized name based on current locale.
     */
    protected function localizedName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->name_ar ?? $this->name,
                    'fr' => $this->name_fr ?? $this->name,
                    default => $this->name,
                };
            }
        );
    }

    /**
     * Get the localized description based on current locale.
     */
    protected function localizedDescription(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->description_ar ?? $this->description,
                    'fr' => $this->description_fr ?? $this->description,
                    default => $this->description,
                };
            }
        );
    }

    /**
     * Scope to get active periods.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get current period.
     */
    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    /**
     * Scope to get periods for a specific academic year.
     */
    public function scopeForAcademicYear($query, $academicYearId)
    {
        return $query->where('academic_year_id', $academicYearId);
    }

    /**
     * Set this period as current and unset others in the same academic year.
     */
    public function setCurrent(): void
    {
        // First, unset all other current periods for this academic year
        static::where('academic_year_id', $this->academic_year_id)
            ->where('id', '!=', $this->id)
            ->update(['is_current' => false]);

        // Set this one as current
        $this->update(['is_current' => true, 'is_active' => true]);
    }

    /**
     * Check if the period is currently active (within date range).
     */
    public function isWithinDateRange(): bool
    {
        $now = now()->toDateString();
        return $now >= $this->start_date->toDateString() && $now <= $this->end_date->toDateString();
    }

    /**
     * Get the formatted period name with order.
     */
    public function getFormattedNameAttribute(): string
    {
        return $this->localized_name . ' (' . $this->getOrdinalNumber() . ')';
    }

    /**
     * Get ordinal number for the period order.
     */
    private function getOrdinalNumber(): string
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return match ($this->order) {
                1 => 'الأول',
                2 => 'الثاني',
                3 => 'الثالث',
                4 => 'الرابع',
                default => (string) $this->order,
            };
        }

        if ($locale === 'fr') {
            return match ($this->order) {
                1 => '1er',
                2 => '2ème',
                3 => '3ème',
                4 => '4ème',
                default => $this->order . 'ème',
            };
        }

        // English
        return match ($this->order) {
            1 => '1st',
            2 => '2nd',
            3 => '3rd',
            default => $this->order . 'th',
        };
    }
}

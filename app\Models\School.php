<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;

class School extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'name_ar',
        'name_fr',
        'description',
        'description_ar',
        'description_fr',
        'email',
        'phone',
        'fax',
        'website',
        'address',
        'address_ar',
        'address_fr',
        'city',
        'state_province',
        'postal_code',
        'country',
        'academic_year_start_month',
        'academic_year_end_month',
        'terms_per_year',
        'default_language',
        'supported_languages',
        'timezone',
        'currency',
        'logo_path',
        'is_active',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'supported_languages' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
        'terms_per_year' => 'integer',
    ];

    /**
     * Get the school's localized name based on current locale.
     */
    protected function localizedName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->name_ar ?? $this->name,
                    'fr' => $this->name_fr ?? $this->name,
                    default => $this->name,
                };
            }
        );
    }

    /**
     * Get the school's localized description based on current locale.
     */
    protected function localizedDescription(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->description_ar ?? $this->description,
                    'fr' => $this->description_fr ?? $this->description,
                    default => $this->description,
                };
            }
        );
    }

    /**
     * Get the school's localized address based on current locale.
     */
    protected function localizedAddress(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->address_ar ?? $this->address,
                    'fr' => $this->address_fr ?? $this->address,
                    default => $this->address,
                };
            }
        );
    }

    /**
     * Get the full address as a formatted string.
     */
    protected function fullAddress(): Attribute
    {
        return Attribute::make(
            get: function () {
                $parts = array_filter([
                    $this->localized_address,
                    $this->city,
                    $this->state_province,
                    $this->postal_code,
                    $this->country,
                ]);

                return implode(', ', $parts);
            }
        );
    }

    /**
     * Scope to get only active schools.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get a specific setting value.
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * Set a specific setting value.
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->settings = $settings;
    }

    /**
     * Get the academic years for the school.
     */
    public function academicYears(): HasMany
    {
        return $this->hasMany(AcademicYear::class)->orderBy('start_date', 'desc');
    }

    /**
     * Get the current academic year for the school.
     */
    public function currentAcademicYear()
    {
        return $this->academicYears()->where('is_current', true)->first();
    }

    /**
     * Get active academic years for the school.
     */
    public function activeAcademicYears(): HasMany
    {
        return $this->academicYears()->where('is_active', true);
    }
}

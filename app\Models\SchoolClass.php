<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SchoolClass extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'classes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'academic_year_id',
        'name',
        'name_ar',
        'name_fr',
        'description',
        'description_ar',
        'description_fr',
        'grade_level',
        'section',
        'capacity',
        'classroom',
        'is_active',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'settings' => 'array',
        'capacity' => 'integer',
    ];

    /**
     * Get the school that owns the class.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the academic year that owns the class.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the localized name based on current locale.
     */
    protected function localizedName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->name_ar ?? $this->name,
                    'fr' => $this->name_fr ?? $this->name,
                    default => $this->name,
                };
            }
        );
    }

    /**
     * Get the localized description based on current locale.
     */
    protected function localizedDescription(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->description_ar ?? $this->description,
                    'fr' => $this->description_fr ?? $this->description,
                    default => $this->description,
                };
            }
        );
    }

    /**
     * Get the full class name with grade level and section.
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $parts = [$this->grade_level];
                if ($this->section) {
                    $parts[] = $this->section;
                }
                return implode(' ', $parts);
            }
        );
    }

    /**
     * Scope to get active classes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get classes for a specific school.
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get classes for a specific academic year.
     */
    public function scopeForAcademicYear($query, $academicYearId)
    {
        return $query->where('academic_year_id', $academicYearId);
    }

    /**
     * Scope to get classes by grade level.
     */
    public function scopeByGradeLevel($query, $gradeLevel)
    {
        return $query->where('grade_level', $gradeLevel);
    }

    /**
     * Get the current enrollment count (placeholder for future implementation).
     */
    public function getCurrentEnrollmentCount(): int
    {
        // TODO: Implement when student enrollment is available
        return 0;
    }

    /**
     * Get the available capacity.
     */
    public function getAvailableCapacity(): int
    {
        return $this->capacity - $this->getCurrentEnrollmentCount();
    }

    /**
     * Check if the class is full.
     */
    public function isFull(): bool
    {
        return $this->getCurrentEnrollmentCount() >= $this->capacity;
    }

    /**
     * Get all available grade levels.
     */
    public static function getGradeLevels(): array
    {
        return [
            'Pre-K',
            'Kindergarten',
            'Grade 1',
            'Grade 2',
            'Grade 3',
            'Grade 4',
            'Grade 5',
            'Grade 6',
            'Grade 7',
            'Grade 8',
            'Grade 9',
            'Grade 10',
            'Grade 11',
            'Grade 12',
        ];
    }

    /**
     * Get all available sections.
     */
    public static function getSections(): array
    {
        return ['A', 'B', 'C', 'D', 'E', 'F'];
    }
}

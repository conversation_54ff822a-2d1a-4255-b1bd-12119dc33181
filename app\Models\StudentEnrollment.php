<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentEnrollment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'academic_year_id',
        'student_id',
        'class_id',
        'student_number',
        'enrollment_date',
        'withdrawal_date',
        'status',
        'notes',
        'is_repeating',
        'previous_school',
        'emergency_contacts',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'enrollment_date' => 'date',
        'withdrawal_date' => 'date',
        'is_repeating' => 'boolean',
        'emergency_contacts' => 'array',
        'settings' => 'array',
    ];

    /**
     * Get the school that owns the enrollment.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the academic year for the enrollment.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the student for the enrollment.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    /**
     * Get the class for the enrollment.
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(SchoolClass::class, 'class_id');
    }

    /**
     * Get the enrollment display name.
     */
    protected function displayName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $student = $this->student?->name ?? 'Unknown Student';
                $class = $this->schoolClass?->name ?? 'Unknown Class';
                $year = $this->academicYear?->name ?? 'Unknown Year';

                return "{$student} - {$class} ({$year})";
            }
        );
    }

    /**
     * Get the enrollment status label.
     */
    protected function statusLabel(): Attribute
    {
        return Attribute::make(
            get: function () {
                return match ($this->status) {
                    'enrolled' => 'Enrolled',
                    'withdrawn' => 'Withdrawn',
                    'transferred' => 'Transferred',
                    'graduated' => 'Graduated',
                    default => 'Unknown',
                };
            }
        );
    }

    /**
     * Check if the enrollment is currently active.
     */
    protected function isActive(): Attribute
    {
        return Attribute::make(
            get: function () {
                return $this->status === 'enrolled' &&
                       (!$this->withdrawal_date || $this->withdrawal_date > now());
            }
        );
    }

    /**
     * Scope to get active enrollments.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'enrolled')
            ->where(function ($q) {
                $q->whereNull('withdrawal_date')
                  ->orWhere('withdrawal_date', '>', now());
            });
    }

    /**
     * Scope to get enrollments for a specific school.
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get enrollments for a specific academic year.
     */
    public function scopeForAcademicYear($query, $academicYearId)
    {
        return $query->where('academic_year_id', $academicYearId);
    }

    /**
     * Scope to get enrollments for a specific student.
     */
    public function scopeForStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    /**
     * Scope to get enrollments for a specific class.
     */
    public function scopeForClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Scope to get enrollments by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get repeating students.
     */
    public function scopeRepeating($query)
    {
        return $query->where('is_repeating', true);
    }

    /**
     * Get all available statuses.
     */
    public static function getStatuses(): array
    {
        return [
            'enrolled' => 'Enrolled',
            'withdrawn' => 'Withdrawn',
            'transferred' => 'Transferred',
            'graduated' => 'Graduated',
        ];
    }

    /**
     * Generate a unique student number.
     */
    public static function generateStudentNumber($schoolId, $academicYearId): string
    {
        $year = substr(date('Y'), -2); // Last 2 digits of current year
        $schoolCode = str_pad($schoolId, 2, '0', STR_PAD_LEFT);

        // Get the next sequential number for this school and year
        $lastEnrollment = static::where('school_id', $schoolId)
            ->where('student_number', 'like', $year . $schoolCode . '%')
            ->orderBy('student_number', 'desc')
            ->first();

        if ($lastEnrollment) {
            $lastNumber = intval(substr($lastEnrollment->student_number, -4));
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $year . $schoolCode . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get enrollment statistics for a school.
     */
    public static function getEnrollmentStats($schoolId, $academicYearId = null)
    {
        $query = static::where('school_id', $schoolId);

        if ($academicYearId) {
            $query->where('academic_year_id', $academicYearId);
        }

        return [
            'total' => $query->count(),
            'enrolled' => $query->where('status', 'enrolled')->count(),
            'withdrawn' => $query->where('status', 'withdrawn')->count(),
            'transferred' => $query->where('status', 'transferred')->count(),
            'graduated' => $query->where('status', 'graduated')->count(),
            'repeating' => $query->where('is_repeating', true)->count(),
        ];
    }
}

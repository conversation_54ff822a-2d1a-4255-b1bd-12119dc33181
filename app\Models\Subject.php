<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Subject extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'school_id',
        'name',
        'name_ar',
        'name_fr',
        'code',
        'description',
        'description_ar',
        'description_fr',
        'grade_levels',
        'coefficient',
        'weekly_hours',
        'category',
        'is_active',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'grade_levels' => 'array',
        'coefficient' => 'decimal:2',
        'weekly_hours' => 'integer',
        'is_active' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Get the school that owns the subject.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the localized name based on current locale.
     */
    protected function localizedName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->name_ar ?? $this->name,
                    'fr' => $this->name_fr ?? $this->name,
                    default => $this->name,
                };
            }
        );
    }

    /**
     * Get the localized description based on current locale.
     */
    protected function localizedDescription(): Attribute
    {
        return Attribute::make(
            get: function () {
                $locale = app()->getLocale();
                return match ($locale) {
                    'ar' => $this->description_ar ?? $this->description,
                    'fr' => $this->description_fr ?? $this->description,
                    default => $this->description,
                };
            }
        );
    }

    /**
     * Get the formatted grade levels string.
     */
    protected function formattedGradeLevels(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->grade_levels || empty($this->grade_levels)) {
                    return 'All Grades';
                }
                return implode(', ', $this->grade_levels);
            }
        );
    }

    /**
     * Scope to get active subjects.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get subjects for a specific school.
     */
    public function scopeForSchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * Scope to get subjects by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get subjects for a specific grade level.
     */
    public function scopeForGradeLevel($query, $gradeLevel)
    {
        return $query->whereJsonContains('grade_levels', $gradeLevel);
    }

    /**
     * Check if the subject is taught in a specific grade level.
     */
    public function isTaughtInGrade(string $gradeLevel): bool
    {
        if (!$this->grade_levels || empty($this->grade_levels)) {
            return true; // If no specific grades, assume it's for all grades
        }
        return in_array($gradeLevel, $this->grade_levels);
    }

    /**
     * Get all available categories.
     */
    public static function getCategories(): array
    {
        return [
            'core' => 'Core Subject',
            'elective' => 'Elective',
            'extracurricular' => 'Extracurricular',
        ];
    }

    /**
     * Get the category label.
     */
    public function getCategoryLabelAttribute(): string
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? $this->category;
    }

    /**
     * Get all available grade levels from SchoolClass.
     */
    public static function getAvailableGradeLevels(): array
    {
        return SchoolClass::getGradeLevels();
    }
}

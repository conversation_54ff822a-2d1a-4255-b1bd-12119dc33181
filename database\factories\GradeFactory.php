<?php

namespace Database\Factories;

use App\Models\AcademicYear;
use App\Models\Grade;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Grade>
 */
class GradeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $gradeTypes = ['assignment', 'quiz', 'exam', 'project', 'participation', 'homework', 'lab', 'presentation'];
        $gradeType = fake()->randomElement($gradeTypes);

        // Generate realistic scores based on grade type
        $maxScore = match ($gradeType) {
            'quiz' => fake()->randomElement([10, 15, 20, 25]),
            'exam' => fake()->randomElement([50, 75, 100]),
            'assignment' => fake()->randomElement([20, 25, 30, 50]),
            'project' => fake()->randomElement([50, 75, 100]),
            'participation' => fake()->randomElement([5, 10]),
            'homework' => fake()->randomElement([10, 15, 20]),
            'lab' => fake()->randomElement([25, 30, 40]),
            'presentation' => fake()->randomElement([25, 50]),
            default => 100,
        };

        // Generate score with realistic distribution (most students get 70-95%)
        $percentage = fake()->randomElement([
            fake()->numberBetween(90, 100), // 20% get A grades
            fake()->numberBetween(80, 89),  // 30% get B grades
            fake()->numberBetween(70, 79),  // 30% get C grades
            fake()->numberBetween(60, 69),  // 15% get D grades
            fake()->numberBetween(0, 59),   // 5% get F grades
        ]);

        $score = round(($percentage / 100) * $maxScore, 2);

        $assessmentDate = fake()->dateTimeBetween('-3 months', 'now');
        $dueDate = fake()->optional(0.7)->dateTimeBetween('-4 months', $assessmentDate);

        $status = fake()->randomElement(['draft', 'published', 'published', 'published']); // 75% published
        $gradedDate = $status === 'published' ? fake()->dateTimeBetween($assessmentDate, 'now') : null;

        $titles = [
            'assignment' => ['Chapter Review', 'Problem Set', 'Essay Assignment', 'Research Paper', 'Case Study'],
            'quiz' => ['Weekly Quiz', 'Pop Quiz', 'Chapter Quiz', 'Review Quiz', 'Vocabulary Quiz'],
            'exam' => ['Midterm Exam', 'Final Exam', 'Unit Test', 'Comprehensive Test', 'Semester Exam'],
            'project' => ['Science Project', 'Group Project', 'Research Project', 'Creative Project', 'Final Project'],
            'participation' => ['Class Participation', 'Discussion Participation', 'Lab Participation', 'Group Work'],
            'homework' => ['Daily Homework', 'Practice Problems', 'Reading Assignment', 'Worksheet', 'Exercise Set'],
            'lab' => ['Lab Experiment', 'Lab Report', 'Practical Lab', 'Lab Activity', 'Lab Assessment'],
            'presentation' => ['Oral Presentation', 'Group Presentation', 'Project Presentation', 'Research Presentation'],
        ];

        $title = fake()->randomElement($titles[$gradeType] ?? ['Assessment']);

        return [
            'school_id' => School::factory(),
            'academic_year_id' => AcademicYear::factory(),
            'student_id' => User::factory()->student(),
            'teacher_id' => User::factory()->teacher(),
            'class_id' => SchoolClass::factory(),
            'subject_id' => Subject::factory(),
            'grade_type' => $gradeType,
            'title' => $title,
            'description' => fake()->optional(0.4)->sentence(),
            'score' => $score,
            'max_score' => $maxScore,
            'assessment_date' => $assessmentDate->format('Y-m-d'),
            'due_date' => $dueDate?->format('Y-m-d'),
            'graded_date' => $gradedDate?->format('Y-m-d'),
            'weight' => fake()->randomElement([1.0, 1.5, 2.0, 2.5, 3.0]),
            'status' => $status,
            'teacher_notes' => fake()->optional(0.2)->sentence(),
            'student_feedback' => fake()->optional(0.3)->sentence(),
            'is_extra_credit' => fake()->boolean(5), // 5% chance
            'is_makeup' => fake()->boolean(3), // 3% chance
            'settings' => [],
        ];
    }

    /**
     * Create a published grade.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'graded_date' => fake()->dateTimeBetween($attributes['assessment_date'] ?? '-1 month', 'now')->format('Y-m-d'),
        ]);
    }

    /**
     * Create a draft grade.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'graded_date' => null,
        ]);
    }

    /**
     * Create an archived grade.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'archived',
            'graded_date' => fake()->dateTimeBetween($attributes['assessment_date'] ?? '-1 month', 'now')->format('Y-m-d'),
        ]);
    }

    /**
     * Create an extra credit grade.
     */
    public function extraCredit(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_extra_credit' => true,
            'title' => 'Extra Credit: ' . $attributes['title'],
        ]);
    }

    /**
     * Create a makeup grade.
     */
    public function makeup(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_makeup' => true,
            'title' => 'Makeup: ' . $attributes['title'],
        ]);
    }

    /**
     * Create a grade for a specific student.
     */
    public function forStudent(User $student): static
    {
        return $this->state(fn (array $attributes) => [
            'student_id' => $student->id,
        ]);
    }

    /**
     * Create a grade for a specific teacher.
     */
    public function forTeacher(User $teacher): static
    {
        return $this->state(fn (array $attributes) => [
            'teacher_id' => $teacher->id,
        ]);
    }

    /**
     * Create a grade for a specific class.
     */
    public function forClass(SchoolClass $class): static
    {
        return $this->state(fn (array $attributes) => [
            'class_id' => $class->id,
        ]);
    }

    /**
     * Create a grade for a specific subject.
     */
    public function forSubject(Subject $subject): static
    {
        return $this->state(fn (array $attributes) => [
            'subject_id' => $subject->id,
        ]);
    }

    /**
     * Create a grade for a specific academic year.
     */
    public function forAcademicYear(AcademicYear $academicYear): static
    {
        return $this->state(fn (array $attributes) => [
            'academic_year_id' => $academicYear->id,
        ]);
    }

    /**
     * Create a grade of a specific type.
     */
    public function ofType(string $type): static
    {
        $titles = [
            'assignment' => ['Chapter Review', 'Problem Set', 'Essay Assignment', 'Research Paper', 'Case Study'],
            'quiz' => ['Weekly Quiz', 'Pop Quiz', 'Chapter Quiz', 'Review Quiz', 'Vocabulary Quiz'],
            'exam' => ['Midterm Exam', 'Final Exam', 'Unit Test', 'Comprehensive Test', 'Semester Exam'],
            'project' => ['Science Project', 'Group Project', 'Research Project', 'Creative Project', 'Final Project'],
            'participation' => ['Class Participation', 'Discussion Participation', 'Lab Participation', 'Group Work'],
            'homework' => ['Daily Homework', 'Practice Problems', 'Reading Assignment', 'Worksheet', 'Exercise Set'],
            'lab' => ['Lab Experiment', 'Lab Report', 'Practical Lab', 'Lab Activity', 'Lab Assessment'],
            'presentation' => ['Oral Presentation', 'Group Presentation', 'Project Presentation', 'Research Presentation'],
        ];

        return $this->state(fn (array $attributes) => [
            'grade_type' => $type,
            'title' => fake()->randomElement($titles[$type] ?? ['Assessment']),
        ]);
    }

    /**
     * Create a high-performing grade (A range).
     */
    public function highPerforming(): static
    {
        return $this->state(function (array $attributes) {
            $percentage = fake()->numberBetween(90, 100);
            $score = round(($percentage / 100) * $attributes['max_score'], 2);

            return [
                'score' => $score,
                'percentage' => $percentage,
            ];
        });
    }

    /**
     * Create a low-performing grade (D or F range).
     */
    public function lowPerforming(): static
    {
        return $this->state(function (array $attributes) {
            $percentage = fake()->numberBetween(0, 69);
            $score = round(($percentage / 100) * $attributes['max_score'], 2);

            return [
                'score' => $score,
                'percentage' => $percentage,
            ];
        });
    }
}

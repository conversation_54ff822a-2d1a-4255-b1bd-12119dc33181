<?php

namespace Database\Factories;

use App\Models\AcademicYear;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Period>
 */
class PeriodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $order = fake()->numberBetween(1, 4);
        $type = fake()->randomElement(['term', 'semester', 'quarter']);

        $termNames = [
            1 => ['First Term', 'الفصل الأول', 'Premier Trimestre'],
            2 => ['Second Term', 'الفصل الثاني', 'Deuxième Trimestre'],
            3 => ['Third Term', 'الفصل الثالث', 'Troisième Trimestre'],
            4 => ['Fourth Term', 'الفصل الرابع', 'Quatrième Trimestre'],
        ];

        $names = $termNames[$order] ?? ['Term ' . $order, 'الفصل ' . $order, 'Trimestre ' . $order];

        return [
            'academic_year_id' => AcademicYear::factory(),
            'name' => $names[0],
            'name_ar' => $names[1],
            'name_fr' => $names[2],
            'description' => fake()->sentence(),
            'description_ar' => fake()->sentence(),
            'description_fr' => fake()->sentence(),
            'order' => $order,
            'type' => $type,
            'start_date' => fake()->dateTimeBetween('2024-01-01', '2024-12-31')->format('Y-m-d'),
            'end_date' => fake()->dateTimeBetween('2024-01-01', '2024-12-31')->format('Y-m-d'),
            'is_active' => fake()->boolean(70),
            'is_current' => false,
            'settings' => [],
        ];
    }

    /**
     * Indicate that the period is current.
     */
    public function current(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_current' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the period is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create a first term.
     */
    public function firstTerm(): static
    {
        return $this->state(fn (array $attributes) => [
            'order' => 1,
            'name' => 'First Term',
            'name_ar' => 'الفصل الأول',
            'name_fr' => 'Premier Trimestre',
        ]);
    }

    /**
     * Create a second term.
     */
    public function secondTerm(): static
    {
        return $this->state(fn (array $attributes) => [
            'order' => 2,
            'name' => 'Second Term',
            'name_ar' => 'الفصل الثاني',
            'name_fr' => 'Deuxième Trimestre',
        ]);
    }
}

<?php

namespace Database\Factories;

use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SchoolClass>
 */
class SchoolClassFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $gradeLevel = fake()->randomElement(SchoolClass::getGradeLevels());
        $section = fake()->randomElement(SchoolClass::getSections());
        $name = $gradeLevel . ($section ? ' ' . $section : '');

        return [
            'school_id' => School::factory(),
            'academic_year_id' => AcademicYear::factory(),
            'name' => $name,
            'name_ar' => $this->getArabicGradeName($gradeLevel) . ($section ? ' ' . $section : ''),
            'name_fr' => $this->getFrenchGradeName($gradeLevel) . ($section ? ' ' . $section : ''),
            'description' => fake()->sentence(),
            'description_ar' => fake()->sentence(),
            'description_fr' => fake()->sentence(),
            'grade_level' => $gradeLevel,
            'section' => $section,
            'capacity' => fake()->numberBetween(20, 35),
            'is_active' => fake()->boolean(85),
            'settings' => [],
        ];
    }

    /**
     * Get Arabic grade name.
     */
    private function getArabicGradeName(string $gradeLevel): string
    {
        $arabicNames = [
            'Pre-K' => 'ما قبل الروضة',
            'Kindergarten' => 'الروضة',
            'Grade 1' => 'الصف الأول',
            'Grade 2' => 'الصف الثاني',
            'Grade 3' => 'الصف الثالث',
            'Grade 4' => 'الصف الرابع',
            'Grade 5' => 'الصف الخامس',
            'Grade 6' => 'الصف السادس',
            'Grade 7' => 'الصف السابع',
            'Grade 8' => 'الصف الثامن',
            'Grade 9' => 'الصف التاسع',
            'Grade 10' => 'الصف العاشر',
            'Grade 11' => 'الصف الحادي عشر',
            'Grade 12' => 'الصف الثاني عشر',
        ];

        return $arabicNames[$gradeLevel] ?? $gradeLevel;
    }

    /**
     * Get French grade name.
     */
    private function getFrenchGradeName(string $gradeLevel): string
    {
        $frenchNames = [
            'Pre-K' => 'Pré-maternelle',
            'Kindergarten' => 'Maternelle',
            'Grade 1' => 'CP',
            'Grade 2' => 'CE1',
            'Grade 3' => 'CE2',
            'Grade 4' => 'CM1',
            'Grade 5' => 'CM2',
            'Grade 6' => '6ème',
            'Grade 7' => '5ème',
            'Grade 8' => '4ème',
            'Grade 9' => '3ème',
            'Grade 10' => '2nde',
            'Grade 11' => '1ère',
            'Grade 12' => 'Terminale',
        ];

        return $frenchNames[$gradeLevel] ?? $gradeLevel;
    }

    /**
     * Create an active class.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive class.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a class for a specific grade level.
     */
    public function gradeLevel(string $gradeLevel): static
    {
        return $this->state(fn (array $attributes) => [
            'grade_level' => $gradeLevel,
            'name' => $gradeLevel . ($attributes['section'] ?? ''),
            'name_ar' => $this->getArabicGradeName($gradeLevel) . ($attributes['section'] ?? ''),
            'name_fr' => $this->getFrenchGradeName($gradeLevel) . ($attributes['section'] ?? ''),
        ]);
    }

    /**
     * Create a class with a specific section.
     */
    public function section(string $section): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => $section,
            'name' => ($attributes['grade_level'] ?? 'Grade 1') . ' ' . $section,
        ]);
    }
}

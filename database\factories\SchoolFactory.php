<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\School>
 */
class SchoolFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' School',
            'name_ar' => 'مدرسة ' . $this->faker->company,
            'name_fr' => 'École ' . $this->faker->company,
            'description' => $this->faker->sentence,
            'description_ar' => 'وصف المدرسة',
            'description_fr' => 'Description de l\'école',
            'email' => $this->faker->companyEmail,
            'phone' => $this->faker->phoneNumber,
            'website' => $this->faker->url,
            'address' => $this->faker->streetAddress,
            'address_ar' => 'العنوان بالعربية',
            'address_fr' => 'Adresse en français',
            'city' => $this->faker->city,
            'state_province' => $this->faker->state,
            'postal_code' => $this->faker->postcode,
            'country' => 'Tunisia',
            'academic_year_start_month' => 'September',
            'academic_year_end_month' => 'June',
            'terms_per_year' => $this->faker->numberBetween(2, 4),
            'default_language' => $this->faker->randomElement(['ar', 'fr', 'en']),
            'supported_languages' => ['ar', 'fr'],
            'timezone' => 'Africa/Tunis',
            'currency' => 'TND',
            'is_active' => true,
            'settings' => [
                'max_students_per_class' => $this->faker->numberBetween(20, 35),
                'grading_scale' => '0-20',
                'attendance_required' => true,
            ],
        ];
    }
}

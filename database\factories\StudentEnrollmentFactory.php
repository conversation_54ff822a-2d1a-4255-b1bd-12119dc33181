<?php

namespace Database\Factories;

use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\StudentEnrollment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\StudentEnrollment>
 */
class StudentEnrollmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $enrollmentDate = fake()->dateTimeBetween('-1 year', 'now');
        $withdrawalDate = fake()->optional(0.1)->dateTimeBetween($enrollmentDate, 'now'); // 10% chance of withdrawal

        $status = $withdrawalDate ? fake()->randomElement(['withdrawn', 'transferred']) : 'enrolled';

        $emergencyContacts = [
            [
                'name' => fake()->name(),
                'relationship' => fake()->randomElement(['Mother', 'Father', 'Guardian', 'Grandmother', 'Grandfather']),
                'phone' => fake()->phoneNumber(),
                'email' => fake()->optional(0.7)->email(),
            ],
            [
                'name' => fake()->name(),
                'relationship' => fake()->randomElement(['Mother', 'Father', 'Guardian', 'Uncle', 'Aunt']),
                'phone' => fake()->phoneNumber(),
                'email' => fake()->optional(0.5)->email(),
            ],
        ];

        return [
            'school_id' => School::factory(),
            'academic_year_id' => AcademicYear::factory(),
            'student_id' => User::factory()->student(),
            'class_id' => SchoolClass::factory(),
            'student_number' => fake()->unique()->numerify('##01####'), // Format: YYSchoolSequence
            'enrollment_date' => $enrollmentDate->format('Y-m-d'),
            'withdrawal_date' => $withdrawalDate?->format('Y-m-d'),
            'status' => $status,
            'notes' => fake()->optional(0.3)->sentence(),
            'is_repeating' => fake()->boolean(5), // 5% chance of repeating
            'previous_school' => fake()->optional(0.2)->company() . ' School',
            'emergency_contacts' => $emergencyContacts,
            'settings' => [],
        ];
    }

    /**
     * Create an enrolled student.
     */
    public function enrolled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'enrolled',
            'withdrawal_date' => null,
        ]);
    }

    /**
     * Create a withdrawn student.
     */
    public function withdrawn(): static
    {
        $enrollmentDate = fake()->dateTimeBetween('-1 year', '-1 month');
        $withdrawalDate = fake()->dateTimeBetween($enrollmentDate, 'now');

        return $this->state(fn (array $attributes) => [
            'status' => 'withdrawn',
            'enrollment_date' => $enrollmentDate->format('Y-m-d'),
            'withdrawal_date' => $withdrawalDate->format('Y-m-d'),
        ]);
    }

    /**
     * Create a transferred student.
     */
    public function transferred(): static
    {
        $enrollmentDate = fake()->dateTimeBetween('-1 year', '-1 month');
        $withdrawalDate = fake()->dateTimeBetween($enrollmentDate, 'now');

        return $this->state(fn (array $attributes) => [
            'status' => 'transferred',
            'enrollment_date' => $enrollmentDate->format('Y-m-d'),
            'withdrawal_date' => $withdrawalDate->format('Y-m-d'),
            'notes' => 'Transferred to ' . fake()->company() . ' School',
        ]);
    }

    /**
     * Create a graduated student.
     */
    public function graduated(): static
    {
        $enrollmentDate = fake()->dateTimeBetween('-1 year', '-3 months');
        $graduationDate = fake()->dateTimeBetween($enrollmentDate, 'now');

        return $this->state(fn (array $attributes) => [
            'status' => 'graduated',
            'enrollment_date' => $enrollmentDate->format('Y-m-d'),
            'withdrawal_date' => $graduationDate->format('Y-m-d'),
            'notes' => 'Successfully completed grade level',
        ]);
    }

    /**
     * Create a repeating student.
     */
    public function repeating(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_repeating' => true,
            'notes' => 'Repeating grade due to academic requirements',
        ]);
    }

    /**
     * Create a transfer student.
     */
    public function transfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'previous_school' => fake()->company() . ' School',
            'notes' => 'Transfer student from ' . fake()->company() . ' School',
        ]);
    }

    /**
     * Create an enrollment for a specific student.
     */
    public function forStudent(User $student): static
    {
        return $this->state(fn (array $attributes) => [
            'student_id' => $student->id,
        ]);
    }

    /**
     * Create an enrollment for a specific class.
     */
    public function forClass(SchoolClass $class): static
    {
        return $this->state(fn (array $attributes) => [
            'class_id' => $class->id,
        ]);
    }

    /**
     * Create an enrollment for a specific academic year.
     */
    public function forAcademicYear(AcademicYear $academicYear): static
    {
        return $this->state(fn (array $attributes) => [
            'academic_year_id' => $academicYear->id,
        ]);
    }

    /**
     * Create an enrollment with a specific student number.
     */
    public function withStudentNumber(string $studentNumber): static
    {
        return $this->state(fn (array $attributes) => [
            'student_number' => $studentNumber,
        ]);
    }

    /**
     * Create an enrollment with specific dates.
     */
    public function withDates(string $enrollmentDate, ?string $withdrawalDate = null): static
    {
        return $this->state(fn (array $attributes) => [
            'enrollment_date' => $enrollmentDate,
            'withdrawal_date' => $withdrawalDate,
        ]);
    }
}

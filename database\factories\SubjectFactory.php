<?php

namespace Database\Factories;

use App\Models\School;
use App\Models\Subject;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subject>
 */
class SubjectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subjects = [
            'Mathematics' => ['الرياضيات', 'Mathématiques', 'MATH'],
            'English Language' => ['اللغة الإنجليزية', 'Anglais', 'ENG'],
            'Arabic Language' => ['اللغة العربية', 'Arabe', 'AR'],
            'French Language' => ['اللغة الفرنسية', 'Français', 'FR'],
            'Science' => ['العلوم', 'Sciences', 'SCI'],
            'Physics' => ['الفيزياء', 'Physique', 'PHY'],
            'Chemistry' => ['الكيمياء', 'Chimie', 'CHEM'],
            'Biology' => ['الأحياء', 'Biologie', 'BIO'],
            'History' => ['التاريخ', 'Histoire', 'HIST'],
            'Geography' => ['الجغرافيا', 'Géographie', 'GEO'],
            'Islamic Studies' => ['التربية الإسلامية', 'Études Islamiques', 'IS'],
            'Physical Education' => ['التربية البدنية', 'Éducation Physique', 'PE'],
            'Art' => ['الفنون', 'Arts', 'ART'],
            'Music' => ['الموسيقى', 'Musique', 'MUS'],
            'Computer Science' => ['علوم الحاسوب', 'Informatique', 'CS'],
        ];

        $subjectName = fake()->randomElement(array_keys($subjects));
        $translations = $subjects[$subjectName];

        $category = fake()->randomElement(['core', 'elective', 'extracurricular']);
        $gradeLevels = fake()->randomElements(Subject::getAvailableGradeLevels(), fake()->numberBetween(2, 6));

        return [
            'school_id' => School::factory(),
            'name' => $subjectName,
            'name_ar' => $translations[0],
            'name_fr' => $translations[1],
            'code' => $translations[2] . fake()->numberBetween(100, 999),
            'description' => fake()->sentence(),
            'description_ar' => fake()->sentence(),
            'description_fr' => fake()->sentence(),
            'grade_levels' => $gradeLevels,
            'coefficient' => fake()->randomFloat(2, 0.5, 3.0),
            'weekly_hours' => fake()->numberBetween(1, 6),
            'category' => $category,
            'is_active' => fake()->boolean(85),
            'settings' => [],
        ];
    }

    /**
     * Create a core subject.
     */
    public function core(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'core',
            'coefficient' => fake()->randomFloat(2, 1.0, 3.0),
            'weekly_hours' => fake()->numberBetween(3, 6),
        ]);
    }

    /**
     * Create an elective subject.
     */
    public function elective(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'elective',
            'coefficient' => fake()->randomFloat(2, 0.5, 2.0),
            'weekly_hours' => fake()->numberBetween(2, 4),
        ]);
    }

    /**
     * Create an extracurricular subject.
     */
    public function extracurricular(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'extracurricular',
            'coefficient' => fake()->randomFloat(2, 0.5, 1.0),
            'weekly_hours' => fake()->numberBetween(1, 2),
        ]);
    }

    /**
     * Create an active subject.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive subject.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a subject for specific grade levels.
     */
    public function forGrades(array $gradeLevels): static
    {
        return $this->state(fn (array $attributes) => [
            'grade_levels' => $gradeLevels,
        ]);
    }

    /**
     * Create a mathematics subject.
     */
    public function mathematics(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Mathematics',
            'name_ar' => 'الرياضيات',
            'name_fr' => 'Mathématiques',
            'code' => 'MATH' . fake()->numberBetween(100, 999),
            'category' => 'core',
            'coefficient' => 3.0,
            'weekly_hours' => 5,
        ]);
    }

    /**
     * Create an English subject.
     */
    public function english(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'English Language',
            'name_ar' => 'اللغة الإنجليزية',
            'name_fr' => 'Anglais',
            'code' => 'ENG' . fake()->numberBetween(100, 999),
            'category' => 'core',
            'coefficient' => 2.5,
            'weekly_hours' => 4,
        ]);
    }
}

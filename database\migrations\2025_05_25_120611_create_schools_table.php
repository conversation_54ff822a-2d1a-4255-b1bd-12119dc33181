<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schools', function (Blueprint $table) {
            $table->id();

            // Basic Information
            $table->string('name');
            $table->string('name_ar')->nullable(); // Arabic name
            $table->string('name_fr')->nullable(); // French name
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_fr')->nullable();

            // Contact Information
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('fax')->nullable();
            $table->string('website')->nullable();

            // Address Information
            $table->text('address');
            $table->text('address_ar')->nullable();
            $table->text('address_fr')->nullable();
            $table->string('city');
            $table->string('state_province')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('Tunisia');

            // Academic Settings
            $table->string('academic_year_start_month')->default('September');
            $table->string('academic_year_end_month')->default('June');
            $table->integer('terms_per_year')->default(3);
            $table->string('default_language')->default('ar');
            $table->json('supported_languages')->default('["ar", "fr"]');

            // System Settings
            $table->string('timezone')->default('Africa/Tunis');
            $table->string('currency')->default('TND');
            $table->string('logo_path')->nullable();
            $table->boolean('is_active')->default(true);

            // Metadata
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schools');
    }
};

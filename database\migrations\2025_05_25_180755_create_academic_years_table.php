<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('academic_years', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');

            // Basic Information
            $table->string('name'); // e.g., "2024-2025"
            $table->string('name_ar')->nullable(); // Arabic name
            $table->string('name_fr')->nullable(); // French name
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_fr')->nullable();

            // Date Range
            $table->date('start_date');
            $table->date('end_date');

            // Status
            $table->boolean('is_active')->default(false);
            $table->boolean('is_current')->default(false); // Only one can be current

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes
            $table->index(['school_id', 'is_current']);
            $table->index(['school_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('academic_years');
    }
};

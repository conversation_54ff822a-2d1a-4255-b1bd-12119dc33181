<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('periods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');

            // Basic Information
            $table->string('name'); // e.g., "First Term", "Semester 1"
            $table->string('name_ar')->nullable(); // Arabic name
            $table->string('name_fr')->nullable(); // French name
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_fr')->nullable();

            // Period Details
            $table->integer('order')->default(1); // 1st term, 2nd term, etc.
            $table->enum('type', ['term', 'semester', 'quarter'])->default('term');

            // Date Range
            $table->date('start_date');
            $table->date('end_date');

            // Status
            $table->boolean('is_active')->default(false);
            $table->boolean('is_current')->default(false); // Only one can be current per academic year

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes
            $table->index(['academic_year_id', 'order']);
            $table->index(['academic_year_id', 'is_current']);
            $table->index(['academic_year_id', 'is_active']);

            // Unique constraint to ensure order is unique within academic year
            $table->unique(['academic_year_id', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('periods');
    }
};

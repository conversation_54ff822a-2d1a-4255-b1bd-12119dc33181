<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('classes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');

            // Basic Information
            $table->string('name'); // e.g., "Grade 5A", "Class 3B"
            $table->string('name_ar')->nullable(); // Arabic name
            $table->string('name_fr')->nullable(); // French name
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_fr')->nullable();

            // Class Details
            $table->string('grade_level'); // e.g., "Grade 1", "Grade 2", etc.
            $table->string('section')->nullable(); // e.g., "A", "B", "C"
            $table->integer('capacity')->default(30); // Maximum number of students
            $table->string('classroom')->nullable(); // Room number or location

            // Status
            $table->boolean('is_active')->default(true);

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes
            $table->index(['school_id', 'academic_year_id']);
            $table->index(['school_id', 'grade_level']);
            $table->index(['school_id', 'is_active']);

            // Unique constraint to prevent duplicate class names within the same academic year
            $table->unique(['school_id', 'academic_year_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('classes');
    }
};

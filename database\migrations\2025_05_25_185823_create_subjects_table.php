<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');

            // Basic Information
            $table->string('name'); // e.g., "Mathematics", "English"
            $table->string('name_ar')->nullable(); // Arabic name
            $table->string('name_fr')->nullable(); // French name
            $table->string('code')->nullable(); // Subject code e.g., "MATH101"
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_fr')->nullable();

            // Academic Details
            $table->json('grade_levels')->nullable(); // Which grades this subject is taught in
            $table->decimal('coefficient', 3, 2)->default(1.00); // Subject weight for grade calculation
            $table->integer('weekly_hours')->default(1); // Hours per week
            $table->enum('category', ['core', 'elective', 'extracurricular'])->default('core');

            // Status
            $table->boolean('is_active')->default(true);

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes
            $table->index(['school_id', 'is_active']);
            $table->index(['school_id', 'category']);

            // Unique constraint to prevent duplicate subject names within the same school
            $table->unique(['school_id', 'name']);
            $table->unique(['school_id', 'code'], 'subjects_school_code_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subjects');
    }
};

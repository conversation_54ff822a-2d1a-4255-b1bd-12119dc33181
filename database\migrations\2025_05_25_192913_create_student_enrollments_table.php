<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_enrollments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('class_id')->constrained('classes')->onDelete('cascade');

            // Enrollment Details
            $table->string('student_number')->unique(); // Unique student ID/number
            $table->date('enrollment_date');
            $table->date('withdrawal_date')->nullable();
            $table->enum('status', ['enrolled', 'withdrawn', 'transferred', 'graduated'])->default('enrolled');
            $table->text('notes')->nullable();

            // Academic Information
            $table->boolean('is_repeating')->default(false); // Is the student repeating this grade?
            $table->string('previous_school')->nullable(); // If transferred from another school
            $table->json('emergency_contacts')->nullable(); // Emergency contact information

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes for performance
            $table->index(['school_id', 'academic_year_id']);
            $table->index(['student_id', 'status']);
            $table->index(['class_id', 'status']);
            $table->index(['academic_year_id', 'status']);
            $table->index('student_number');

            // Unique constraint to prevent duplicate enrollments
            $table->unique(['student_id', 'class_id', 'academic_year_id'], 'unique_student_class_year');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_enrollments');
    }
};

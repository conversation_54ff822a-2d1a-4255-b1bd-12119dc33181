<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('grades', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('teacher_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('class_id')->constrained('classes')->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');

            // Grade Information
            $table->string('grade_type')->default('assignment'); // assignment, quiz, exam, project, participation
            $table->string('title'); // Name of the assessment
            $table->text('description')->nullable();
            $table->decimal('score', 5, 2); // The actual score (e.g., 85.50)
            $table->decimal('max_score', 5, 2); // Maximum possible score (e.g., 100.00)
            $table->decimal('percentage', 5, 2)->nullable(); // Calculated percentage
            $table->string('letter_grade', 2)->nullable(); // A+, A, B+, etc.
            $table->decimal('gpa_points', 3, 2)->nullable(); // GPA points (4.0 scale)

            // Assessment Details
            $table->date('assessment_date'); // When the assessment was given
            $table->date('due_date')->nullable(); // When it was due (for assignments)
            $table->date('graded_date')->nullable(); // When it was graded
            $table->decimal('weight', 5, 2)->default(1.00); // Weight in final grade calculation

            // Status and Notes
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->text('teacher_notes')->nullable(); // Private notes for teacher
            $table->text('student_feedback')->nullable(); // Feedback visible to student
            $table->boolean('is_extra_credit')->default(false);
            $table->boolean('is_makeup')->default(false); // Makeup assessment

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes for performance
            $table->index(['school_id', 'academic_year_id']);
            $table->index(['student_id', 'subject_id']);
            $table->index(['teacher_id', 'class_id']);
            $table->index(['class_id', 'subject_id']);
            $table->index(['assessment_date', 'grade_type']);
            $table->index(['status', 'graded_date']);

            // Unique constraint to prevent duplicate grades for same assessment
            $table->unique(['student_id', 'class_id', 'subject_id', 'title', 'assessment_date'], 'unique_student_assessment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grades');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('grading_systems', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');

            // System Information
            $table->string('name'); // e.g., "Tunisian 20-Point", "Primary Letter Grade", "ECTS"
            $table->string('name_ar')->nullable();
            $table->string('name_fr')->nullable();
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_fr')->nullable();

            // System Type and Configuration
            $table->enum('system_type', ['letter', '20-point', '10-point', 'ECTS', 'percentage'])->default('20-point');
            $table->decimal('min_score', 5, 2)->default(0); // Minimum possible score
            $table->decimal('max_score', 5, 2)->default(20); // Maximum possible score
            $table->decimal('passing_score', 5, 2)->default(10); // Minimum passing score

            // Grade Level Application
            $table->json('applicable_grade_levels'); // Which grade levels use this system
            $table->json('grade_scale_mapping'); // Grade boundaries and equivalents

            // Academic Period Configuration
            $table->boolean('supports_trimesters')->default(true);
            $table->boolean('supports_semesters')->default(false);
            $table->json('assessment_types'); // Supported assessment types

            // Status and Dates
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->date('effective_from')->nullable();
            $table->date('effective_until')->nullable();

            // Settings
            $table->json('settings')->nullable(); // Additional configuration

            $table->timestamps();

            // Indexes
            $table->index(['school_id', 'system_type']);
            $table->index(['school_id', 'is_active']);
            $table->index(['school_id', 'is_default']);

            // Unique constraint for default system per school
            $table->unique(['school_id', 'system_type', 'is_default'], 'unique_default_system');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grading_systems');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('grades', function (Blueprint $table) {
            $table->foreignId('grading_system_id')->nullable()->after('subject_id')->constrained()->onDelete('set null');
            $table->index('grading_system_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('grades', function (Blueprint $table) {
            $table->dropForeign(['grading_system_id']);
            $table->dropIndex(['grading_system_id']);
            $table->dropColumn('grading_system_id');
        });
    }
};

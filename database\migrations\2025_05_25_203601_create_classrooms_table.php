<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('classrooms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');

            // Classroom Information
            $table->string('name'); // e.g., "Room 101", "Science Lab A", "Computer Lab"
            $table->string('name_ar')->nullable();
            $table->string('name_fr')->nullable();
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('description_fr')->nullable();

            // Physical Details
            $table->string('room_number')->nullable(); // Physical room number
            $table->string('building')->nullable(); // Building name/number
            $table->string('floor')->nullable(); // Floor level
            $table->integer('capacity'); // Maximum number of students

            // Classroom Type and Features
            $table->enum('classroom_type', ['regular', 'laboratory', 'computer_lab', 'library', 'gymnasium', 'auditorium', 'art_room', 'music_room', 'workshop'])->default('regular');
            $table->json('features')->nullable(); // Equipment and features (projector, computers, etc.)
            $table->json('subjects_suitable')->nullable(); // Subjects this classroom is suitable for

            // Availability and Status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_available')->default(true);
            $table->text('maintenance_notes')->nullable();
            $table->date('last_maintenance')->nullable();

            // Location Details
            $table->string('location_details')->nullable(); // Additional location info
            $table->json('accessibility_features')->nullable(); // Wheelchair access, etc.

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes for performance
            $table->index(['school_id', 'is_active']);
            $table->index(['school_id', 'classroom_type']);
            $table->index(['school_id', 'building', 'floor']);
            $table->index('room_number');

            // Unique constraint for room number within school
            $table->unique(['school_id', 'room_number'], 'unique_room_number_per_school');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('classrooms');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('classroom_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained()->onDelete('cascade');
            $table->foreignId('classroom_id')->constrained()->onDelete('cascade');
            $table->foreignId('class_id')->constrained('classes')->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->foreignId('teacher_id')->constrained('users')->onDelete('cascade');

            // Schedule Information
            $table->enum('day_of_week', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);
            $table->time('start_time');
            $table->time('end_time');
            $table->integer('period_number')->nullable(); // Period 1, 2, 3, etc.

            // Date Range
            $table->date('effective_from');
            $table->date('effective_until');

            // Schedule Type and Status
            $table->enum('schedule_type', ['regular', 'exam', 'makeup', 'special'])->default('regular');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_recurring')->default(true); // Weekly recurring

            // Additional Information
            $table->text('notes')->nullable();
            $table->json('exceptions')->nullable(); // Dates when this schedule doesn't apply

            // Settings
            $table->json('settings')->nullable(); // Additional flexible settings

            $table->timestamps();

            // Indexes for performance
            $table->index(['school_id', 'academic_year_id']);
            $table->index(['classroom_id', 'day_of_week', 'start_time']);
            $table->index(['class_id', 'subject_id']);
            $table->index(['teacher_id', 'day_of_week']);
            $table->index(['effective_from', 'effective_until']);

            // Unique constraint to prevent double booking
            $table->unique(['classroom_id', 'day_of_week', 'start_time', 'effective_from'], 'unique_classroom_time_slot');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('classroom_schedules');
    }
};

<?php

namespace Database\Seeders;

use App\Models\AcademicYear;
use App\Models\Period;
use App\Models\School;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AcademicYearSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school = School::first();

        if (!$school) {
            $this->command->error('No school found. Please run SchoolSeeder first.');
            return;
        }

        // Create current academic year (2024-2025)
        $currentYear = AcademicYear::create([
            'school_id' => $school->id,
            'name' => '2024-2025',
            'name_ar' => '2024-2025',
            'name_fr' => '2024-2025',
            'description' => 'Current academic year 2024-2025',
            'description_ar' => 'العام الدراسي الحالي 2024-2025',
            'description_fr' => 'Année académique actuelle 2024-2025',
            'start_date' => '2024-09-01',
            'end_date' => '2025-06-30',
            'is_active' => true,
            'is_current' => true,
        ]);

        // Create periods for current year
        $periods = [
            [
                'name' => 'First Term',
                'name_ar' => 'الفصل الأول',
                'name_fr' => 'Premier Trimestre',
                'order' => 1,
                'start_date' => '2024-09-01',
                'end_date' => '2024-12-20',
                'is_current' => true,
            ],
            [
                'name' => 'Second Term',
                'name_ar' => 'الفصل الثاني',
                'name_fr' => 'Deuxième Trimestre',
                'order' => 2,
                'start_date' => '2025-01-08',
                'end_date' => '2025-03-28',
                'is_current' => false,
            ],
            [
                'name' => 'Third Term',
                'name_ar' => 'الفصل الثالث',
                'name_fr' => 'Troisième Trimestre',
                'order' => 3,
                'start_date' => '2025-04-07',
                'end_date' => '2025-06-30',
                'is_current' => false,
            ],
        ];

        foreach ($periods as $periodData) {
            Period::create([
                'academic_year_id' => $currentYear->id,
                'name' => $periodData['name'],
                'name_ar' => $periodData['name_ar'],
                'name_fr' => $periodData['name_fr'],
                'description' => "Academic period for {$periodData['name']}",
                'description_ar' => "فترة أكاديمية لـ {$periodData['name_ar']}",
                'description_fr' => "Période académique pour {$periodData['name_fr']}",
                'order' => $periodData['order'],
                'type' => 'term',
                'start_date' => $periodData['start_date'],
                'end_date' => $periodData['end_date'],
                'is_active' => true,
                'is_current' => $periodData['is_current'],
            ]);
        }

        // Create previous academic year (2023-2024)
        $previousYear = AcademicYear::create([
            'school_id' => $school->id,
            'name' => '2023-2024',
            'name_ar' => '2023-2024',
            'name_fr' => '2023-2024',
            'description' => 'Previous academic year 2023-2024',
            'description_ar' => 'العام الدراسي السابق 2023-2024',
            'description_fr' => 'Année académique précédente 2023-2024',
            'start_date' => '2023-09-01',
            'end_date' => '2024-06-30',
            'is_active' => false,
            'is_current' => false,
        ]);

        // Create periods for previous year
        foreach ($periods as $index => $periodData) {
            Period::create([
                'academic_year_id' => $previousYear->id,
                'name' => $periodData['name'],
                'name_ar' => $periodData['name_ar'],
                'name_fr' => $periodData['name_fr'],
                'description' => "Academic period for {$periodData['name']} (2023-2024)",
                'description_ar' => "فترة أكاديمية لـ {$periodData['name_ar']} (2023-2024)",
                'description_fr' => "Période académique pour {$periodData['name_fr']} (2023-2024)",
                'order' => $periodData['order'],
                'type' => 'term',
                'start_date' => str_replace('2024', '2023', str_replace('2025', '2024', $periodData['start_date'])),
                'end_date' => str_replace('2024', '2023', str_replace('2025', '2024', $periodData['end_date'])),
                'is_active' => false,
                'is_current' => false,
            ]);
        }

        // Create next academic year (2025-2026)
        $nextYear = AcademicYear::create([
            'school_id' => $school->id,
            'name' => '2025-2026',
            'name_ar' => '2025-2026',
            'name_fr' => '2025-2026',
            'description' => 'Next academic year 2025-2026',
            'description_ar' => 'العام الدراسي القادم 2025-2026',
            'description_fr' => 'Prochaine année académique 2025-2026',
            'start_date' => '2025-09-01',
            'end_date' => '2026-06-30',
            'is_active' => true,
            'is_current' => false,
        ]);

        $this->command->info('Academic years and periods created successfully!');
    }
}

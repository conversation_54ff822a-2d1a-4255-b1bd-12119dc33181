<?php

namespace Database\Seeders;

use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ClassSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school = School::first();
        $currentAcademicYear = AcademicYear::where('school_id', $school->id)
            ->where('is_current', true)
            ->first();

        if (!$school || !$currentAcademicYear) {
            $this->command->error('No school or current academic year found. Please run SchoolSeeder and AcademicYearSeeder first.');
            return;
        }

        // Define grade levels and sections for a typical school
        $gradeStructure = [
            'Pre-K' => ['A'],
            'Kindergarten' => ['A', 'B'],
            'Grade 1' => ['A', 'B'],
            'Grade 2' => ['A', 'B'],
            'Grade 3' => ['A', 'B', 'C'],
            'Grade 4' => ['A', 'B', 'C'],
            'Grade 5' => ['A', 'B', 'C'],
            'Grade 6' => ['A', 'B'],
            'Grade 7' => ['A', 'B'],
            'Grade 8' => ['A', 'B'],
            'Grade 9' => ['A', 'B'],
            'Grade 10' => ['A'],
            'Grade 11' => ['A'],
            'Grade 12' => ['A'],
        ];

        $roomCounter = 101;

        foreach ($gradeStructure as $gradeLevel => $sections) {
            foreach ($sections as $section) {
                $name = $gradeLevel . ' ' . $section;

                // Get localized names
                $nameAr = $this->getArabicGradeName($gradeLevel) . ' ' . $section;
                $nameFr = $this->getFrenchGradeName($gradeLevel) . ' ' . $section;

                SchoolClass::create([
                    'school_id' => $school->id,
                    'academic_year_id' => $currentAcademicYear->id,
                    'name' => $name,
                    'name_ar' => $nameAr,
                    'name_fr' => $nameFr,
                    'description' => "Class for {$gradeLevel} Section {$section}",
                    'description_ar' => "فصل {$nameAr}",
                    'description_fr' => "Classe {$nameFr}",
                    'grade_level' => $gradeLevel,
                    'section' => $section,
                    'capacity' => $this->getCapacityForGrade($gradeLevel),
                    'is_active' => true,
                    'settings' => [],
                ]);

                $roomCounter++;
            }
        }

        $this->command->info('Classes created successfully for the current academic year!');
    }

    /**
     * Get Arabic grade name.
     */
    private function getArabicGradeName(string $gradeLevel): string
    {
        $arabicNames = [
            'Pre-K' => 'ما قبل الروضة',
            'Kindergarten' => 'الروضة',
            'Grade 1' => 'الصف الأول',
            'Grade 2' => 'الصف الثاني',
            'Grade 3' => 'الصف الثالث',
            'Grade 4' => 'الصف الرابع',
            'Grade 5' => 'الصف الخامس',
            'Grade 6' => 'الصف السادس',
            'Grade 7' => 'الصف السابع',
            'Grade 8' => 'الصف الثامن',
            'Grade 9' => 'الصف التاسع',
            'Grade 10' => 'الصف العاشر',
            'Grade 11' => 'الصف الحادي عشر',
            'Grade 12' => 'الصف الثاني عشر',
        ];

        return $arabicNames[$gradeLevel] ?? $gradeLevel;
    }

    /**
     * Get French grade name.
     */
    private function getFrenchGradeName(string $gradeLevel): string
    {
        $frenchNames = [
            'Pre-K' => 'Pré-maternelle',
            'Kindergarten' => 'Maternelle',
            'Grade 1' => 'CP',
            'Grade 2' => 'CE1',
            'Grade 3' => 'CE2',
            'Grade 4' => 'CM1',
            'Grade 5' => 'CM2',
            'Grade 6' => '6ème',
            'Grade 7' => '5ème',
            'Grade 8' => '4ème',
            'Grade 9' => '3ème',
            'Grade 10' => '2nde',
            'Grade 11' => '1ère',
            'Grade 12' => 'Terminale',
        ];

        return $frenchNames[$gradeLevel] ?? $gradeLevel;
    }

    /**
     * Get appropriate capacity for each grade level.
     */
    private function getCapacityForGrade(string $gradeLevel): int
    {
        return match ($gradeLevel) {
            'Pre-K', 'Kindergarten' => 20,
            'Grade 1', 'Grade 2' => 25,
            'Grade 3', 'Grade 4', 'Grade 5' => 30,
            'Grade 6', 'Grade 7', 'Grade 8' => 32,
            'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12' => 35,
            default => 30,
        };
    }
}

<?php

namespace Database\Seeders;

use App\Models\GradingSystem;
use App\Models\School;
use Illuminate\Database\Seeder;

class GradingSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school = School::first();

        if (!$school) {
            $this->command->error('No school found. Please run SchoolSeeder first.');
            return;
        }

        // Check if grading systems already exist for this school
        $existingSystems = GradingSystem::where('school_id', $school->id)->count();

        if ($existingSystems > 0) {
            $this->command->info('Grading systems already exist for this school. Skipping...');
            return;
        }

        // Create default Tunisian grading systems
        GradingSystem::createDefaultSystems($school);

        $this->command->info('Default Tunisian grading systems created successfully!');
        $this->command->info('Created systems for:');
        $this->command->info('- Primary Letter Grade System (Pre-K to Grade 2)');
        $this->command->info('- 10-Point Scale (Grades 3-5, First Trimester)');
        $this->command->info('- Tunisian 20-Point Scale (Grades 6-12)');
        $this->command->info('- ECTS European Scale (Grades 11-12)');
    }
}

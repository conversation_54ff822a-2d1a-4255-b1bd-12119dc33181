<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class RoleBasedUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        // Create teacher user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'teacher',
                'email_verified_at' => now(),
            ]
        );

        // Create student user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Fatima Zahra',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'email_verified_at' => now(),
            ]
        );

        // Create parent user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Mohamed Salah',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'parent',
                'email_verified_at' => now(),
            ]
        );

        // Create additional test users
        User::factory()->admin()->count(2)->create();
        User::factory()->teacher()->count(5)->create();
        User::factory()->student()->count(20)->create();
        User::factory()->parent()->count(10)->create();
    }
}

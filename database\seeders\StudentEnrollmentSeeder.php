<?php

namespace Database\Seeders;

use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\StudentEnrollment;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StudentEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school = School::first();
        $currentAcademicYear = AcademicYear::where('school_id', $school->id)
            ->where('is_current', true)
            ->first();

        if (!$school || !$currentAcademicYear) {
            $this->command->error('No school or current academic year found. Please run SchoolSeeder and AcademicYearSeeder first.');
            return;
        }

        // Get all students
        $students = User::where('role', 'student')->get();

        if ($students->isEmpty()) {
            $this->command->error('No students found. Please run StudentUserSeeder first.');
            return;
        }

        // Get all classes
        $classes = SchoolClass::where('school_id', $school->id)->get();

        if ($classes->isEmpty()) {
            $this->command->error('No classes found. Please run ClassSeeder first.');
            return;
        }

        $enrollmentCount = 0;
        $studentsEnrolled = collect();

        // Distribute students across classes based on grade level and capacity
        foreach ($classes as $class) {
            $availableStudents = $students->diff($studentsEnrolled);

            if ($availableStudents->isEmpty()) {
                break; // No more students to enroll
            }

            // Calculate how many students to enroll in this class
            // Try to fill classes to about 80-90% capacity for realism
            $targetEnrollment = (int) ($class->capacity * fake()->numberBetween(75, 95) / 100);
            $studentsToEnroll = min($targetEnrollment, $availableStudents->count());

            // Randomly select students for this class
            $selectedStudents = $availableStudents->random(min($studentsToEnroll, $availableStudents->count()));

            foreach ($selectedStudents as $student) {
                // Generate unique student number
                $studentNumber = StudentEnrollment::generateStudentNumber($school->id, $currentAcademicYear->id);

                // Create realistic enrollment data
                $enrollmentDate = fake()->dateTimeBetween($currentAcademicYear->start_date, 'now');

                // Small chance of withdrawal/transfer (5%)
                $status = fake()->randomElement(['enrolled', 'enrolled', 'enrolled', 'enrolled', 'enrolled', 'enrolled', 'enrolled', 'enrolled', 'enrolled', 'withdrawn']);
                $withdrawalDate = null;

                if ($status === 'withdrawn') {
                    $withdrawalDate = fake()->dateTimeBetween($enrollmentDate, 'now');
                }

                // Emergency contacts
                $emergencyContacts = [
                    [
                        'name' => fake()->name(),
                        'relationship' => fake()->randomElement(['Mother', 'Father', 'Guardian']),
                        'phone' => fake()->phoneNumber(),
                        'email' => fake()->optional(0.8)->email(),
                        'address' => fake()->address(),
                    ],
                    [
                        'name' => fake()->name(),
                        'relationship' => fake()->randomElement(['Mother', 'Father', 'Guardian', 'Grandmother', 'Grandfather']),
                        'phone' => fake()->phoneNumber(),
                        'email' => fake()->optional(0.6)->email(),
                        'address' => fake()->optional(0.7)->address(),
                    ],
                ];

                // Check if enrollment already exists
                $existingEnrollment = StudentEnrollment::where('student_id', $student->id)
                    ->where('class_id', $class->id)
                    ->where('academic_year_id', $currentAcademicYear->id)
                    ->first();

                if (!$existingEnrollment) {
                    StudentEnrollment::create([
                        'school_id' => $school->id,
                        'academic_year_id' => $currentAcademicYear->id,
                        'student_id' => $student->id,
                        'class_id' => $class->id,
                        'student_number' => $studentNumber,
                        'enrollment_date' => $enrollmentDate->format('Y-m-d'),
                        'withdrawal_date' => $withdrawalDate?->format('Y-m-d'),
                        'status' => $status,
                        'notes' => $this->generateEnrollmentNotes($status, $class),
                        'is_repeating' => fake()->boolean(3), // 3% chance of repeating
                        'previous_school' => fake()->optional(0.15)->company() . ' School', // 15% are transfers
                        'emergency_contacts' => $emergencyContacts,
                        'settings' => [],
                    ]);

                    $enrollmentCount++;
                    $studentsEnrolled->push($student);
                }
            }
        }

        // Create some additional enrollments for different statuses
        $this->createSpecialEnrollments($school, $currentAcademicYear, $classes, $students, $studentsEnrolled);

        $this->command->info("Student enrollments created successfully!");
        $this->command->info("Created {$enrollmentCount} student enrollments for the current academic year.");

        // Show summary statistics
        $stats = StudentEnrollment::getEnrollmentStats($school->id, $currentAcademicYear->id);
        $this->command->info("Enrollment Statistics:");
        $this->command->info("- Total: {$stats['total']}");
        $this->command->info("- Enrolled: {$stats['enrolled']}");
        $this->command->info("- Withdrawn: {$stats['withdrawn']}");
        $this->command->info("- Transferred: {$stats['transferred']}");
        $this->command->info("- Graduated: {$stats['graduated']}");
        $this->command->info("- Repeating: {$stats['repeating']}");
    }

    /**
     * Generate realistic enrollment notes based on status and class.
     */
    private function generateEnrollmentNotes(string $status, SchoolClass $class): ?string
    {
        return match ($status) {
            'enrolled' => fake()->optional(0.3)->randomElement([
                "Regular enrollment for {$class->grade_level}",
                "Student shows good academic potential",
                "Enrolled with full documentation",
                "Parent requested this specific class",
            ]),
            'withdrawn' => fake()->randomElement([
                "Family relocated to another city",
                "Personal family circumstances",
                "Transferred to private school",
                "Medical reasons requiring home schooling",
            ]),
            'transferred' => fake()->randomElement([
                "Transferred to sister school in the network",
                "Family moved to different district",
                "Requested transfer for academic program",
                "Administrative transfer",
            ]),
            'graduated' => "Successfully completed {$class->grade_level}",
            default => null,
        };
    }

    /**
     * Create some special enrollment cases for testing.
     */
    private function createSpecialEnrollments(School $school, AcademicYear $academicYear, $classes, $students, $studentsEnrolled)
    {
        $availableStudents = $students->diff($studentsEnrolled);

        if ($availableStudents->count() < 5) {
            return; // Not enough students for special cases
        }

        // Create a few transferred students
        $transferStudents = $availableStudents->random(min(3, $availableStudents->count()));
        foreach ($transferStudents as $student) {
            $class = $classes->random();
            $enrollmentDate = fake()->dateTimeBetween('-6 months', '-2 months');
            $transferDate = fake()->dateTimeBetween($enrollmentDate, '-1 month');

            StudentEnrollment::create([
                'school_id' => $school->id,
                'academic_year_id' => $academicYear->id,
                'student_id' => $student->id,
                'class_id' => $class->id,
                'student_number' => StudentEnrollment::generateStudentNumber($school->id, $academicYear->id),
                'enrollment_date' => $enrollmentDate->format('Y-m-d'),
                'withdrawal_date' => $transferDate->format('Y-m-d'),
                'status' => 'transferred',
                'notes' => 'Transferred to ' . fake()->company() . ' School due to family relocation',
                'is_repeating' => false,
                'previous_school' => fake()->company() . ' Academy',
                'emergency_contacts' => [
                    [
                        'name' => fake()->name(),
                        'relationship' => 'Mother',
                        'phone' => fake()->phoneNumber(),
                        'email' => fake()->email(),
                    ]
                ],
            ]);
        }

        // Create a few repeating students
        $remainingStudents = $availableStudents->diff($transferStudents);
        if ($remainingStudents->count() >= 2) {
            $repeatingStudents = $remainingStudents->random(min(2, $remainingStudents->count()));
            foreach ($repeatingStudents as $student) {
                $class = $classes->random();

                StudentEnrollment::create([
                    'school_id' => $school->id,
                    'academic_year_id' => $academicYear->id,
                    'student_id' => $student->id,
                    'class_id' => $class->id,
                    'student_number' => StudentEnrollment::generateStudentNumber($school->id, $academicYear->id),
                    'enrollment_date' => $academicYear->start_date,
                    'status' => 'enrolled',
                    'notes' => 'Repeating ' . $class->grade_level . ' to strengthen academic foundation',
                    'is_repeating' => true,
                    'emergency_contacts' => [
                        [
                            'name' => fake()->name(),
                            'relationship' => 'Father',
                            'phone' => fake()->phoneNumber(),
                            'email' => fake()->email(),
                        ]
                    ],
                ]);
            }
        }
    }
}

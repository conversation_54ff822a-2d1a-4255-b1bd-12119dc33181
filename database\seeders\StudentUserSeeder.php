<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class StudentUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create diverse student names representing different cultures
        $students = [
            // Arabic names
            ['name' => '<PERSON>', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => 'Fatima Al-Zahra', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => '<PERSON><PERSON>', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => '<PERSON>', 'email' => 'khali<PERSON>.<PERSON><PERSON><PERSON>@student.neweduco.com', 'preferred_language' => 'ar'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => 'Zainab Mahmoud', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => 'Hassan Nasser', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],
            ['name' => 'Maryam Saleh', 'email' => '<EMAIL>', 'preferred_language' => 'ar'],

            // English names
            ['name' => 'Emma Johnson', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Liam Smith', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Olivia Brown', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Noah Davis', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Ava Wilson', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'William Miller', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Sophia Garcia', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'James Rodriguez', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Isabella Martinez', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Benjamin Anderson', 'email' => '<EMAIL>', 'preferred_language' => 'en'],

            // French names
            ['name' => 'Léa Dubois', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Lucas Martin', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Emma Moreau', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Hugo Laurent', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Chloé Bernard', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Louis Petit', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Manon Robert', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Gabriel Richard', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Jade Durand', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],
            ['name' => 'Arthur Leroy', 'email' => '<EMAIL>', 'preferred_language' => 'fr'],

            // Additional diverse names
            ['name' => 'Priya Patel', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Raj Kumar', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Maria Santos', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Carlos Mendez', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Yuki Tanaka', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Chen Wei', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Amara Okafor', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Dmitri Volkov', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Elena Rossi', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
            ['name' => 'Kai Nielsen', 'email' => '<EMAIL>', 'preferred_language' => 'en'],
        ];

        foreach ($students as $studentData) {
            $existingStudent = User::where('email', $studentData['email'])->first();

            if (!$existingStudent) {
                User::create([
                    'name' => $studentData['name'],
                    'email' => $studentData['email'],
                    'password' => Hash::make('password'), // Default password
                    'role' => 'student',
                    'preferred_language' => $studentData['preferred_language'],
                    'email_verified_at' => now(),
                ]);
            }
        }

        $this->command->info('Student users created successfully!');
        $this->command->info('Created ' . count($students) . ' student accounts.');
        $this->command->info('Default password for all students: password');
    }
}

<?php

return [
    // Navigation
    'dashboard' => 'Dashboard',
    'settings' => 'Settings',
    'profile' => 'Profile',
    'logout' => 'Logout',
    'login' => 'Login',
    'register' => 'Register',

    // Common Actions
    'save' => 'Save',
    'cancel' => 'Cancel',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'create' => 'Create',
    'update' => 'Update',
    'view' => 'View',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'download' => 'Download',
    'upload' => 'Upload',
    'submit' => 'Submit',
    'reset' => 'Reset',
    'clear' => 'Clear',
    'confirm' => 'Confirm',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'close' => 'Close',
    'open' => 'Open',
    'add' => 'Add',
    'remove' => 'Remove',
    'select' => 'Select',
    'choose' => 'Choose',
    'browse' => 'Browse',
    'save_settings' => 'Save Settings',

    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'enabled' => 'Enabled',
    'disabled' => 'Disabled',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'completed' => 'Completed',
    'in_progress' => 'In Progress',
    'draft' => 'Draft',
    'published' => 'Published',

    // Messages
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'loading' => 'Loading...',
    'saving' => 'Saving...',
    'processing' => 'Processing...',
    'please_wait' => 'Please wait...',
    'no_data' => 'No data available',
    'no_results' => 'No results found',
    'try_again' => 'Try again',

    // Table & Pagination
    'columns' => 'Columns',
    'rows_per_page' => 'Rows per page',
    'page' => 'Page',
    'of' => 'of',
    'rows_selected' => 'row(s) selected',
    'select_all' => 'Select all',
    'select_row' => 'Select row',
    'go_to_first_page' => 'Go to first page',
    'go_to_previous_page' => 'Go to previous page',
    'go_to_next_page' => 'Go to next page',
    'go_to_last_page' => 'Go to last page',

    // Academic Year
    'select_academic_year' => 'Select Academic Year',
    'search_academic_years' => 'Search academic years...',
    'no_academic_years_found' => 'No academic years found',
    'current' => 'Current',

    // Confirmation Messages
    'are_you_sure' => 'Are you sure?',
    'delete_confirmation' => 'Are you sure you want to delete this item?',
    'unsaved_changes' => 'You have unsaved changes. Are you sure you want to leave?',

    // Form Labels
    'name' => 'Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'city' => 'City',
    'country' => 'Country',
    'description' => 'Description',
    'date' => 'Date',
    'time' => 'Time',
    'status' => 'Status',
    'type' => 'Type',
    'category' => 'Category',
    'title' => 'Title',
    'content' => 'Content',
    'notes' => 'Notes',
    'comments' => 'Comments',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',

    // Time & Date
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'tomorrow' => 'Tomorrow',
    'this_week' => 'This Week',
    'last_week' => 'Last Week',
    'next_week' => 'Next Week',
    'this_month' => 'This Month',
    'last_month' => 'Last Month',
    'next_month' => 'Next Month',
    'this_year' => 'This Year',
    'last_year' => 'Last Year',
    'next_year' => 'Next Year',

    // Languages
    'language' => 'Language',
    'languages' => 'Languages',
    'arabic' => 'Arabic',
    'french' => 'French',
    'english' => 'English',

    // School Related
    'school' => 'School',
    'schools' => 'Schools',
    'student' => 'Student',
    'students' => 'Students',
    'teacher' => 'Teacher',
    'teachers' => 'Teachers',
    'parent' => 'Parent',
    'parents' => 'Parents',
    'admin' => 'Administrator',
    'class' => 'Class',
    'classes' => 'Classes',
    'subject' => 'Subject',
    'subjects' => 'Subjects',
    'grade' => 'Grade',
    'grades' => 'Grades',
    'attendance' => 'Attendance',
    'academic_year' => 'Academic Year',
    'academic_years' => 'Academic Years',
    'term' => 'Term',
    'semester' => 'Semester',
    'exam' => 'Exam',
    'exams' => 'Exams',
    'assignment' => 'Assignment',
    'assignments' => 'Assignments',
    'homework' => 'Homework',
    'lesson' => 'Lesson',
    'lessons' => 'Lessons',
    'schedule' => 'Schedule',
    'timetable' => 'Timetable',
    'report' => 'Report',
    'reports' => 'Reports',

    // Teacher Assignments
    'teacher_assignments' => 'Teacher Assignments',
    'manage_teacher_assignments_description' => 'Manage teacher assignments to classes and subjects for :school',
    'search_teacher_assignments' => 'Search teacher assignments...',
    'no_assignments_match_filters' => 'No assignments match your current filters.',
    'no_teacher_assignments_found' => 'No teacher assignments found. Get started by creating your first assignment.',

    // Dashboard
    'dashboard' => [
        'welcome' => [
            'admin' => 'Welcome, Administrator',
            'teacher' => 'Welcome, Teacher',
            'student' => 'Welcome, Student',
            'parent' => 'Welcome, Parent',
            'user' => 'Welcome',
        ],
        'description' => [
            'admin' => 'Manage your school system and oversee all operations.',
            'teacher' => 'Access your classes, students, and teaching tools.',
            'student' => 'View your grades, assignments, and academic progress.',
            'parent' => 'Monitor your children\'s academic progress and school activities.',
            'default' => 'Welcome to your dashboard.',
        ],
    ],
];

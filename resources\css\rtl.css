/* RTL (Right-to-Left) Support for Arabic and other RTL languages */

/* Base RTL styles */
[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* Typography adjustments for RTL */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
    text-align: right;
}

[dir="rtl"] p,
[dir="rtl"] div,
[dir="rtl"] span {
    text-align: right;
}

/* Form elements RTL adjustments */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
    text-align: right;
    direction: rtl;
}

[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="url"],
[dir="rtl"] input[type="tel"] {
    direction: ltr;
    text-align: left;
}

/* Button adjustments */

[dir="rtl"] .flex-row {
    flex-direction: row-reverse;
}

[dir="rtl"] .flex-row-reverse {
    flex-direction: row;
}

/* Margin and padding adjustments */
[dir="rtl"] .ml-1 { margin-left: 0; margin-right: 0.25rem; }
[dir="rtl"] .ml-2 { margin-left: 0; margin-right: 0.5rem; }
[dir="rtl"] .ml-3 { margin-left: 0; margin-right: 0.75rem; }
[dir="rtl"] .ml-4 { margin-left: 0; margin-right: 1rem; }
[dir="rtl"] .ml-5 { margin-left: 0; margin-right: 1.25rem; }
[dir="rtl"] .ml-6 { margin-left: 0; margin-right: 1.5rem; }
[dir="rtl"] .ml-8 { margin-left: 0; margin-right: 2rem; }
[dir="rtl"] .ml-auto { margin-left: 0; margin-right: auto; }

[dir="rtl"] .mr-1 { margin-right: 0; margin-left: 0.25rem; }
[dir="rtl"] .mr-2 { margin-right: 0; margin-left: 0.5rem; }
[dir="rtl"] .mr-3 { margin-right: 0; margin-left: 0.75rem; }
[dir="rtl"] .mr-4 { margin-right: 0; margin-left: 1rem; }
[dir="rtl"] .mr-5 { margin-right: 0; margin-left: 1.25rem; }
[dir="rtl"] .mr-6 { margin-right: 0; margin-left: 1.5rem; }
[dir="rtl"] .mr-8 { margin-right: 0; margin-left: 2rem; }
[dir="rtl"] .mr-auto { margin-right: 0; margin-left: auto; }

[dir="rtl"] .pl-1 { padding-left: 0; padding-right: 0.25rem; }
[dir="rtl"] .pl-2 { padding-left: 0; padding-right: 0.5rem; }
[dir="rtl"] .pl-3 { padding-left: 0; padding-right: 0.75rem; }
[dir="rtl"] .pl-4 { padding-left: 0; padding-right: 1rem; }
[dir="rtl"] .pl-5 { padding-left: 0; padding-right: 1.25rem; }
[dir="rtl"] .pl-6 { padding-left: 0; padding-right: 1.5rem; }
[dir="rtl"] .pl-8 { padding-left: 0; padding-right: 2rem; }

[dir="rtl"] .pr-1 { padding-right: 0; padding-left: 0.25rem; }
[dir="rtl"] .pr-2 { padding-right: 0; padding-left: 0.5rem; }
[dir="rtl"] .pr-3 { padding-right: 0; padding-left: 0.75rem; }
[dir="rtl"] .pr-4 { padding-right: 0; padding-left: 1rem; }
[dir="rtl"] .pr-5 { padding-right: 0; padding-left: 1.25rem; }
[dir="rtl"] .pr-6 { padding-right: 0; padding-left: 1.5rem; }
[dir="rtl"] .pr-8 { padding-right: 0; padding-left: 2rem; }

/* Border radius adjustments */
[dir="rtl"] .rounded-l { border-radius: 0 0.375rem 0.375rem 0; }
[dir="rtl"] .rounded-r { border-radius: 0.375rem 0 0 0.375rem; }
[dir="rtl"] .rounded-tl { border-radius: 0 0.375rem 0 0; }
[dir="rtl"] .rounded-tr { border-radius: 0.375rem 0 0 0; }
[dir="rtl"] .rounded-bl { border-radius: 0 0 0.375rem 0; }
[dir="rtl"] .rounded-br { border-radius: 0 0 0 0.375rem; }

/* Text alignment */
[dir="rtl"] .text-left { text-align: right; }
[dir="rtl"] .text-right { text-align: left; }

/* Float adjustments */
[dir="rtl"] .float-left { float: right; }
[dir="rtl"] .float-right { float: left; }

/* Position adjustments */
[dir="rtl"] .left-0 { left: auto; right: 0; }
[dir="rtl"] .left-1 { left: auto; right: 0.25rem; }
[dir="rtl"] .left-2 { left: auto; right: 0.5rem; }
[dir="rtl"] .left-3 { left: auto; right: 0.75rem; }
[dir="rtl"] .left-4 { left: auto; right: 1rem; }

[dir="rtl"] .right-0 { right: auto; left: 0; }
[dir="rtl"] .right-1 { right: auto; left: 0.25rem; }
[dir="rtl"] .right-2 { right: auto; left: 0.5rem; }
[dir="rtl"] .right-3 { right: auto; left: 0.75rem; }
[dir="rtl"] .right-4 { right: auto; left: 1rem; }

/* Transform adjustments for icons */
[dir="rtl"] .transform-flip {
    transform: scaleX(-1);
}

/* Dropdown menu adjustments */
[dir="rtl"] .dropdown-menu {
    left: auto;
    right: 0;
}

/* Navigation adjustments */
[dir="rtl"] .nav-item {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .nav-item:last-child {
    margin-right: 0;
}

/* Card and component adjustments */
[dir="rtl"] .card-header,
[dir="rtl"] .card-body,
[dir="rtl"] .card-footer {
    text-align: right;
}

/* Table adjustments */
[dir="rtl"] table {
    direction: rtl;
}

[dir="rtl"] th,
[dir="rtl"] td {
    text-align: right;
}

[dir="rtl"] th:first-child,
[dir="rtl"] td:first-child {
    border-radius: 0 0.375rem 0.375rem 0;
}

[dir="rtl"] th:last-child,
[dir="rtl"] td:last-child {
    border-radius: 0.375rem 0 0 0.375rem;
}

/* Modal adjustments */
[dir="rtl"] .modal-header,
[dir="rtl"] .modal-body,
[dir="rtl"] .modal-footer {
    text-align: right;
}

/* Breadcrumb adjustments */
[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* Sidebar adjustments */
[dir="rtl"] .sidebar {
    left: auto;
    right: 0;
}

[dir="rtl"] .sidebar-toggle {
    left: auto;
    right: 1rem;
}

/* Custom RTL utilities */
.rtl-flip {
    transform: scaleX(-1);
}

[dir="rtl"] .ltr-only {
    direction: ltr;
    text-align: left;
}

[dir="ltr"] .rtl-only {
    display: none;
}

[dir="rtl"] .ltr-only {
    display: none;
}

/* Arabic font improvements */
[dir="rtl"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
}

/* Number and date inputs should remain LTR */
[dir="rtl"] input[type="number"],
[dir="rtl"] input[type="date"],
[dir="rtl"] input[type="time"],
[dir="rtl"] input[type="datetime-local"] {
    direction: ltr;
    text-align: left;
}

/* Specific adjustments for form layouts */
[dir="rtl"] .form-group label {
    text-align: right;
}

[dir="rtl"] .form-check {
    text-align: right;
}

[dir="rtl"] .form-check-input {
    margin-left: 0.25rem;
    margin-right: 0;
}

/* Grid adjustments */
[dir="rtl"] .grid {
    direction: ltr;
}

[dir="rtl"] .grid > * {
    direction: rtl;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .mobile-menu {
        left: auto;
        right: 0;
    }

    [dir="rtl"] .mobile-sidebar {
        transform: translateX(100%);
    }

    [dir="rtl"] .mobile-sidebar.open {
        transform: translateX(0);
    }
}

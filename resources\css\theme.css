/* Theme CSS Variables */

:root {
  /* Base colors */
  --background: #ffffff;
  --foreground: #333333;
  
  /* Component colors */
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #4b5563;
  --accent: #e0f2fe;
  --accent-foreground: #1e3a8a;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  
  /* UI element colors */
  --card: #ffffff;
  --card-foreground: #333333;
  --popover: #ffffff;
  --popover-foreground: #333333;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #3b82f6;
  
  /* Utility colors */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  
  /* Typography */
  --font-sans: 'Inter, sans-serif';
  --font-serif: 'Georgia, serif';
  --font-mono: 'Menlo, monospace';
  
  /* Other properties */
  --radius: 0.375rem;
}

[data-theme="dark"] {
  /* Base colors */
  --background: #171717;
  --foreground: #e5e5e5;
  
  /* Component colors */
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #262626;
  --secondary-foreground: #e5e5e5;
  --accent: #1e3a8a;
  --accent-foreground: #bfdbfe;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  
  /* UI element colors */
  --card: #262626;
  --card-foreground: #e5e5e5;
  --popover: #262626;
  --popover-foreground: #e5e5e5;
  --border: #404040;
  --input: #404040;
  --ring: #3b82f6;
  
  /* Utility colors */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
}

import { useEffect } from 'react';
import { usePage } from '@inertiajs/react';
import { useAcademicYearStore } from '@/stores/academic-year-store';

interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
    start_date: string;
    end_date: string;
    is_current: boolean;
    is_active: boolean;
}

interface PageProps {
    academicYears?: AcademicYear[];
    currentAcademicYear?: AcademicYear | null;
    [key: string]: any;
}

/**
 * Hook that initializes the academic year store with data from Inertia props.
 * This should be called in layouts or pages that need academic year data.
 */
export function useAcademicYearInitializer() {
    const { props } = usePage<PageProps>();
    const { initializeFromProps } = useAcademicYearStore();

    useEffect(() => {
        // Initialize the store with data from Inertia props
        const academicYears = props.academicYears || [];
        const currentAcademicYear = props.currentAcademicYear || null;

        if (academicYears.length > 0) {
            initializeFromProps(academicYears, currentAcademicYear);
        }
    }, [props.academicYears, props.currentAcademicYear, initializeFromProps]);
}

/**
 * Hook to check if academic year data is available
 */
export function useAcademicYearAvailable() {
    const { props } = usePage<PageProps>();
    return (props.academicYears?.length || 0) > 0;
}

/**
 * Hook to get academic year data from Inertia props
 */
export function useAcademicYearProps() {
    const { props } = usePage<PageProps>();
    return {
        academicYears: props.academicYears || [],
        currentAcademicYear: props.currentAcademicYear || null,
    };
}

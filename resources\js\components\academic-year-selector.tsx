import * as React from "react"
import { Calendar, Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useAcademicYearStore } from "@/stores/academic-year-store"
import { useTranslation } from "@/hooks/use-translation"
import { router } from "@inertiajs/react"

interface AcademicYear {
  id: number
  name: string
  localized_name: string
  start_date: string
  end_date: string
  is_current: boolean
  is_active: boolean
}

interface AcademicYearSelectorProps {
  className?: string
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg"
  showIcon?: boolean
  placeholder?: string
}

export function AcademicYearSelector({
  className,
  variant = "outline",
  size = "default",
  showIcon = true,
  placeholder,
}: AcademicYearSelectorProps) {
  const { t } = useTranslation()
  const [open, setOpen] = React.useState(false)
  
  const {
    selectedAcademicYear,
    availableAcademicYears,
    setSelectedAcademicYear,
    isLoading,
  } = useAcademicYearStore()

  const handleSelect = (academicYear: AcademicYear) => {
    setSelectedAcademicYear(academicYear)
    setOpen(false)
    
    // Optionally reload the page with the new academic year context
    // This ensures all data is refreshed with the new academic year
    const currentUrl = new URL(window.location.href)
    const searchParams = new URLSearchParams(currentUrl.search)
    
    // Add or update the academic year parameter
    searchParams.set('academic_year_id', academicYear.id.toString())
    
    // Navigate to the same page with the new academic year parameter
    router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
      preserveState: true,
      preserveScroll: true,
      only: ['data'], // Only reload data, not the entire page
    })
  }

  const getDisplayText = () => {
    if (!selectedAcademicYear) {
      return placeholder || t('select_academic_year')
    }
    
    return selectedAcademicYear.localized_name || selectedAcademicYear.name
  }

  const getYearStatus = (academicYear: AcademicYear) => {
    if (academicYear.is_current) {
      return t('current')
    }
    if (!academicYear.is_active) {
      return t('inactive')
    }
    return null
  }

  if (availableAcademicYears.length === 0) {
    return null
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={variant}
          size={size}
          role="combobox"
          aria-expanded={open}
          aria-label={t('select_academic_year')}
          className={cn(
            "justify-between",
            !selectedAcademicYear && "text-muted-foreground",
            className
          )}
          disabled={isLoading}
        >
          <div className="flex items-center">
            {showIcon && <Calendar className="mr-2 h-4 w-4 shrink-0" />}
            <span className="truncate">{getDisplayText()}</span>
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <Command>
          <CommandInput 
            placeholder={t('search_academic_years')} 
            className="h-9"
          />
          <CommandList>
            <CommandEmpty>{t('no_academic_years_found')}</CommandEmpty>
            <CommandGroup>
              {availableAcademicYears.map((academicYear) => {
                const isSelected = selectedAcademicYear?.id === academicYear.id
                const status = getYearStatus(academicYear)
                
                return (
                  <CommandItem
                    key={academicYear.id}
                    value={`${academicYear.name} ${academicYear.localized_name}`}
                    onSelect={() => handleSelect(academicYear)}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center">
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          isSelected ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {academicYear.localized_name || academicYear.name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(academicYear.start_date).getFullYear()} - {new Date(academicYear.end_date).getFullYear()}
                        </span>
                      </div>
                    </div>
                    {status && (
                      <span className={cn(
                        "text-xs px-2 py-1 rounded-full",
                        academicYear.is_current 
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                      )}>
                        {status}
                      </span>
                    )}
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

// Compact version for use in smaller spaces
export function AcademicYearSelectorCompact({
  className,
}: {
  className?: string
}) {
  return (
    <AcademicYearSelector
      className={cn("w-[200px]", className)}
      variant="outline"
      size="sm"
      showIcon={false}
    />
  )
}

// Hook to get current academic year ID for API calls
export function useCurrentAcademicYearId() {
  const selectedAcademicYear = useAcademicYearStore(state => state.selectedAcademicYear)
  return selectedAcademicYear?.id || null
}

// Hook to get current academic year object
export function useCurrentAcademicYear() {
  return useAcademicYearStore(state => state.selectedAcademicYear)
}

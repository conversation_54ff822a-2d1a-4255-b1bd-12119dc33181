import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BookOpen, LayoutGrid, Settings, School, Users, GraduationCap, Calendar, FileText, CalendarDays, Clock, Building2, Book, UserCheck, UserPlus, Award, BarChart3 } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

const adminNavItems: NavItem[] = [
    {
        title: 'School Settings',
        href: '/admin/school-settings',
        icon: School,
    },
    {
        title: 'Academic Years',
        href: '/admin/academic-years',
        icon: CalendarDays,
    },
    {
        title: 'Periods/Terms',
        href: '/admin/periods',
        icon: Clock,
    },
    {
        title: 'Classes',
        href: '/admin/classes',
        icon: Building2,
    },
    {
        title: 'Subjects',
        href: '/admin/subjects',
        icon: Book,
    },
    {
        title: 'Teacher Assignments',
        href: '/admin/teacher-assignments',
        icon: UserCheck,
    },
    {
        title: 'Student Enrollments',
        href: '/admin/student-enrollments',
        icon: UserPlus,
    },
    {
        title: 'User Management',
        href: '/admin/users',
        icon: Users,
    },
    {
        title: 'System Settings',
        href: '/admin/system',
        icon: Settings,
    },
];

const academicNavItems: NavItem[] = [
    {
        title: 'Classes',
        href: '/academic/classes',
        icon: School,
    },
    {
        title: 'Subjects',
        href: '/academic/subjects',
        icon: BookOpen,
    },
    {
        title: 'Students',
        href: '/academic/students',
        icon: Users,
    },
    {
        title: 'Teachers',
        href: '/academic/teachers',
        icon: GraduationCap,
    },
    {
        title: 'Grades',
        href: '/academic/grades',
        icon: FileText,
    },
    {
        title: 'Attendance',
        href: '/academic/attendance',
        icon: Calendar,
    },
];

const teacherNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/teacher/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Grades',
        href: '/teacher/grades',
        icon: BarChart3,
    },
];

const studentNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/student/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'My Grades',
        href: '/student/grades',
        icon: Award,
    },
];


export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;
    const userRole = auth.user?.role;

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} label="Platform" />

                {userRole === 'admin' && (
                    <>
                        <NavMain items={adminNavItems} label="Administration" />
                        <NavMain items={academicNavItems} label="Academic" />
                    </>
                )}

                {userRole === 'teacher' && (
                    <NavMain items={teacherNavItems} label="Teaching" />
                )}

                {userRole === 'student' && (
                    <NavMain items={studentNavItems} label="Student" />
                )}
            </SidebarContent>

        </Sidebar>
    );
}

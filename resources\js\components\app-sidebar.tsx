import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { BookOpen, LayoutGrid, Settings, School, Users, GraduationCap, Calendar, FileText, CalendarDays, Clock, Building2, Book } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

const adminNavItems: NavItem[] = [
    {
        title: 'School Settings',
        href: '/admin/school-settings',
        icon: School,
    },
    {
        title: 'Academic Years',
        href: '/admin/academic-years',
        icon: CalendarDays,
    },
    {
        title: 'Periods/Terms',
        href: '/admin/periods',
        icon: Clock,
    },
    {
        title: 'Classes',
        href: '/admin/classes',
        icon: Building2,
    },
    {
        title: 'Subjects',
        href: '/admin/subjects',
        icon: Book,
    },
    {
        title: 'User Management',
        href: '/admin/users',
        icon: Users,
    },
    {
        title: 'System Settings',
        href: '/admin/system',
        icon: Settings,
    },
];

const academicNavItems: NavItem[] = [
    {
        title: 'Classes',
        href: '/academic/classes',
        icon: School,
    },
    {
        title: 'Subjects',
        href: '/academic/subjects',
        icon: BookOpen,
    },
    {
        title: 'Students',
        href: '/academic/students',
        icon: Users,
    },
    {
        title: 'Teachers',
        href: '/academic/teachers',
        icon: GraduationCap,
    },
    {
        title: 'Grades',
        href: '/academic/grades',
        icon: FileText,
    },
    {
        title: 'Attendance',
        href: '/academic/attendance',
        icon: Calendar,
    },
];


export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} label="Platform" />
                <NavMain items={adminNavItems} label="Administration" />
                <NavMain items={academicNavItems} label="Academic" />
            </SidebarContent>

        </Sidebar>
    );
}

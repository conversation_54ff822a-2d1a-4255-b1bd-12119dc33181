import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface DashboardStatsProps {
    stats: Record<string, number>;
    role: string;
}

export function DashboardStats({ stats, role }: DashboardStatsProps) {
    const getStatLabel = (key: string, role: string): string => {
        const labels: Record<string, Record<string, string>> = {
            admin: {
                total_users: 'Total Users',
                total_teachers: 'Teachers',
                total_students: 'Students',
                total_parents: 'Parents',
            },
            teacher: {
                my_classes: 'My Classes',
                my_students: 'My Students',
                pending_grades: 'Pending Grades',
                attendance_today: 'Attendance Today',
            },
            student: {
                my_subjects: 'My Subjects',
                current_average: 'Current Average',
                attendance_rate: 'Attendance Rate',
                assignments_due: 'Assignments Due',
            },
            parent: {
                my_children: 'My Children',
                notifications: 'Notifications',
                upcoming_events: 'Upcoming Events',
                payment_due: 'Payment Due',
            },
        };

        return labels[role]?.[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    };

    const formatStatValue = (key: string, value: number): string => {
        if (key === 'current_average') {
            return value > 0 ? `${value.toFixed(1)}%` : 'N/A';
        }
        if (key === 'attendance_rate') {
            return value > 0 ? `${value.toFixed(1)}%` : 'N/A';
        }
        return value.toString();
    };

    return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(stats).map(([key, value]) => (
                <Card key={key}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            {getStatLabel(key, role)}
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatStatValue(key, value)}</div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}

import { Button } from '@/components/ui/button';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { type QuickAction } from '@/types';
import { Link } from '@inertiajs/react';
import * as Icons from 'lucide-react';

interface QuickActionsProps {
    actions: QuickAction[];
}

export function QuickActions({ actions }: QuickActionsProps) {
    const getIcon = (iconName: string) => {
        const IconComponent = Icons[iconName as keyof typeof Icons] as React.ComponentType<{ className?: string }>;
        return IconComponent ? <IconComponent className="h-4 w-4" /> : null;
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid gap-2 md:grid-cols-2">
                    {actions.map((action, index) => (
                        <Button
                            key={index}
                            variant="outline"
                            className="justify-start"
                            asChild
                        >
                            <Link href={action.href}>
                                {getIcon(action.icon)}
                                <span className="ml-2">{action.title}</span>
                            </Link>
                        </Button>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
}

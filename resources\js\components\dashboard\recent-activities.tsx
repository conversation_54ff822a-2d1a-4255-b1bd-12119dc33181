import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

interface RecentActivitiesProps {
    activities: string[];
}

export function RecentActivities({ activities }: RecentActivitiesProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Recent Activities</CardTitle>
            </CardHeader>
            <CardContent>
                <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                        {activities.length > 0 ? (
                            activities.map((activity, index) => (
                                <div
                                    key={index}
                                    className="flex items-center space-x-2 rounded-lg border p-2 text-sm"
                                >
                                    <div className="h-2 w-2 rounded-full bg-blue-500" />
                                    <span>{activity}</span>
                                </div>
                            ))
                        ) : (
                            <div className="text-center text-muted-foreground">
                                No recent activities
                            </div>
                        )}
                    </div>
                </ScrollArea>
            </CardContent>
        </Card>
    );
}

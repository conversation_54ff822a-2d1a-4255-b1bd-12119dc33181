import { useTranslation } from '@/hooks/use-translation';
import { type User } from '@/types';

interface RoleWelcomeProps {
    user: User;
}

export function RoleWelcome({ user }: RoleWelcomeProps) {
    const { t } = useTranslation();

    const getRoleTitle = (role: string): string => {
        const titles: Record<string, string> = {
            admin: t('dashboard.welcome.admin'),
            teacher: t('dashboard.welcome.teacher'),
            student: t('dashboard.welcome.student'),
            parent: t('dashboard.welcome.parent'),
        };
        return titles[role] || t('dashboard.welcome.user');
    };

    const getRoleDescription = (role: string): string => {
        const descriptions: Record<string, string> = {
            admin: t('dashboard.description.admin'),
            teacher: t('dashboard.description.teacher'),
            student: t('dashboard.description.student'),
            parent: t('dashboard.description.parent'),
        };
        return descriptions[role] || t('dashboard.description.default');
    };

    return (
        <div className="mb-6">
            <h1 className="text-3xl font-bold tracking-tight">
                {getRoleTitle(user.role)}, {user.name}!
            </h1>
            <p className="text-muted-foreground">
                {getRoleDescription(user.role)}
            </p>
        </div>
    );
}

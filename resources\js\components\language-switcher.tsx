import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Languages, Check } from 'lucide-react';

interface Language {
    code: string;
    name: string;
    native_name: string;
    direction: 'ltr' | 'rtl';
}

interface LanguageData {
    languages: Language[];
    current: string;
}

export function LanguageSwitcher() {
    const [languageData, setLanguageData] = useState<LanguageData | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        fetchLanguages();
    }, []);

    const fetchLanguages = async () => {
        try {
            const response = await fetch(route('api.languages'));
            const data = await response.json();
            setLanguageData(data);
        } catch (error) {
            console.error('Failed to fetch languages:', error);
        }
    };

    const switchLanguage = async (locale: string) => {
        if (isLoading || locale === languageData?.current) return;

        setIsLoading(true);

        try {
            // Use Inertia router to navigate to language switch route
            router.visit(route('language.switch', { locale }), {
                method: 'get',
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    // Update the current language in state
                    if (languageData) {
                        setLanguageData({
                            ...languageData,
                            current: locale
                        });
                    }

                    // Update document direction for RTL languages
                    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
                    const direction = rtlLanguages.includes(locale) ? 'rtl' : 'ltr';
                    document.documentElement.dir = direction;
                    document.documentElement.lang = locale;
                },
                onFinish: () => {
                    setIsLoading(false);
                }
            });
        } catch (error) {
            console.error('Failed to switch language:', error);
            setIsLoading(false);
        }
    };

    if (!languageData || languageData.languages.length <= 1) {
        return null;
    }

    // const currentLanguage = languageData.languages.find(
    //     lang => lang.code === languageData.current
    // );

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 px-0"
                    disabled={isLoading}
                >
                    <Languages className="h-4 w-4" />
                    <span className="sr-only">Switch language</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
                {languageData.languages.map((language) => (
                    <DropdownMenuItem
                        key={language.code}
                        onClick={() => switchLanguage(language.code)}
                        className="flex items-center justify-between cursor-pointer"
                        disabled={isLoading}
                    >
                        <div className="flex flex-col">
                            <span className="font-medium">
                                {language.native_name}
                                {/* (
                                    <span className="text-xs text-muted-foreground">
                                        {language.name}
                                    </span>
                                ) */}
                            </span>

                        </div>
                        {language.code === languageData.current && (
                            <Check className="h-4 w-4" />
                        )}
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

// Alternative compact version for mobile or space-constrained areas
export function CompactLanguageSwitcher() {
    const [languageData, setLanguageData] = useState<LanguageData | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        fetchLanguages();
    }, []);

    const fetchLanguages = async () => {
        try {
            const response = await fetch(route('api.languages'));
            const data = await response.json();
            setLanguageData(data);
        } catch (error) {
            console.error('Failed to fetch languages:', error);
        }
    };

    const switchLanguage = async (locale: string) => {
        if (isLoading || locale === languageData?.current) return;

        setIsLoading(true);

        try {
            router.visit(route('language.switch', { locale }), {
                method: 'get',
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    if (languageData) {
                        setLanguageData({
                            ...languageData,
                            current: locale
                        });
                    }

                    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
                    const direction = rtlLanguages.includes(locale) ? 'rtl' : 'ltr';
                    document.documentElement.dir = direction;
                    document.documentElement.lang = locale;
                },
                onFinish: () => {
                    setIsLoading(false);
                }
            });
        } catch (error) {
            console.error('Failed to switch language:', error);
            setIsLoading(false);
        }
    };

    if (!languageData || languageData.languages.length <= 1) {
        return null;
    }

    const currentLanguage = languageData.languages.find(
        lang => lang.code === languageData.current
    );

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-xs"
                    disabled={isLoading}
                >
                    {currentLanguage?.code.toUpperCase() || 'EN'}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
                {languageData.languages.map((language) => (
                    <DropdownMenuItem
                        key={language.code}
                        onClick={() => switchLanguage(language.code)}
                        className="flex items-center justify-between cursor-pointer text-xs"
                        disabled={isLoading}
                    >
                        <span>{language.code.toUpperCase()}</span>
                        {language.code === languageData.current && (
                            <Check className="h-3 w-3" />
                        )}
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

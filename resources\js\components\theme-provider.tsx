import React, { createContext, useContext, useEffect } from "react";
import { useEditorStore } from "../store/editor-store";
import { applyThemeToElement } from "../utils/apply-theme";

type Theme = "dark" | "light";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
};

type Coords = { x: number; y: number };

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: (coords?: Coords) => void;
};

const initialState: ThemeProviderState = {
  theme: "light",
  setTheme: () => null,
  toggleTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { themeState, setThemeState } = useEditorStore();

  useEffect(() => {
    const root = document.documentElement;
    if (!root) return;

    console.log("Applying theme in ThemeProvider:", themeState);
    applyThemeToElement(themeState, root);

    // Force a re-render by applying the theme to the document element
    const currentMode = themeState.currentMode;
    root.setAttribute("data-theme", currentMode);

    // Apply all theme properties as CSS variables
    Object.entries(themeState.styles[currentMode]).forEach(([key, value]) => {
      root.style.setProperty(`--${key}`, value);
    });
  }, [themeState]);

  const handleThemeChange = (newMode: Theme) => {
    setThemeState({ ...themeState, currentMode: newMode });
  };

  const handleThemeToggle = (coords?: Coords) => {
    const root = document.documentElement;
    const newMode = themeState.currentMode === "light" ? "dark" : "light";

    // Check if the browser supports view transitions
    const prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;

    if (!document.startViewTransition || prefersReducedMotion) {
      handleThemeChange(newMode);
      return;
    }

    // Use view transitions API for a smooth theme change animation
    const transition = document.startViewTransition(() => {
      handleThemeChange(newMode);
    });

    // If coordinates are provided, create a circular animation from that point
    if (coords) {
      const x = coords.x;
      const y = coords.y;

      const endRadius = Math.hypot(
        Math.max(x, innerWidth - x),
        Math.max(y, innerHeight - y)
      );

      // Apply the animation
      transition.ready.then(() => {
        const clipPath = [
          `circle(0px at ${x}px ${y}px)`,
          `circle(${endRadius}px at ${x}px ${y}px)`,
        ];

        document.documentElement.animate(
          {
            clipPath: themeState.currentMode === "light" ? clipPath : [...clipPath].reverse(),
          },
          {
            duration: 300,
            easing: "ease-in",
            pseudoElement: themeState.currentMode === "light"
              ? "::view-transition-new(root)"
              : "::view-transition-old(root)",
          }
        );
      });
    }
  };

  const value = {
    theme: themeState.currentMode,
    setTheme: handleThemeChange,
    toggleTheme: handleThemeToggle,
  };

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

import { useEffect } from "react";
import { useTheme } from "./theme-provider";
import { useThemePresetStore } from "../store/theme-preset-store";
import { useEditorStore } from "../store/editor-store";

interface ThemeSelectorProps {
  className?: string;
}

export function ThemeSelector({ className = "" }: ThemeSelectorProps) {
  const { theme, setTheme } = useTheme();
  const { presets, loadSavedPresets } = useThemePresetStore();
  const { themeState, applyThemePreset } = useEditorStore();

  useEffect(() => {
    // Load any saved presets when the component mounts
    loadSavedPresets();
  }, [loadSavedPresets]);

  const handleThemePresetChange = (presetName: string) => {
    applyThemePreset(presetName);
  };

  const handleModeToggle = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Theme Mode</h3>
        <div className="flex items-center space-x-4">
          <button
            onClick={handleModeToggle}
            className={`px-4 py-2 rounded-md ${
              theme === "light"
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground"
            }`}
          >
            Light
          </button>
          <button
            onClick={handleModeToggle}
            className={`px-4 py-2 rounded-md ${
              theme === "dark"
                ? "bg-primary text-primary-foreground"
                : "bg-muted text-muted-foreground"
            }`}
          >
            Dark
          </button>
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-medium">Theme Presets</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">dddd
          {Object.entries(presets).map(([key, preset]) => (
            <button
              key={key}
              onClick={() => handleThemePresetChange(key)}
              className={`p-4 rounded-md border transition-all ${
                themeState.preset === key
                  ? "border-primary ring-2 ring-primary"
                  : "border-border hover:border-primary"
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="font-medium">{preset.label}</span>
                {preset.source === "SAVED" && (
                  <span className="text-xs bg-secondary text-secondary-foreground px-2 py-1 rounded">
                    Custom
                  </span>
                )}
              </div>
              <div className="mt-2 grid grid-cols-5 gap-1">
                {["primary", "secondary", "accent", "background", "card"].map(
                  (color) => (
                    <div
                      key={color}
                      className="w-full h-6 rounded"
                      style={{
                        backgroundColor:
                          preset.styles[theme][
                            color as keyof typeof preset.styles[typeof theme]
                          ],
                      }}
                    />
                  )
                )}
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

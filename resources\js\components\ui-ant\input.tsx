import React from 'react';
import { Input as AntInput } from 'antd';
import { cn } from '@/lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    const baseInputClasses = "border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-all outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-[#444] dark:border-[#666] dark:text-white";

    // For password type, use AntInput.Password
    if (type === 'password') {
      return (
        <AntInput.Password
          ref={ref as any}
          type={type}
          className={cn(baseInputClasses, className)}
          style={{
            backgroundColor: 'var(--input-bg, #444)',
            color: 'var(--input-text, white)'
          }}
          {...props}
        />
      );
    }

    // For textarea, use AntInput.TextArea
    if (type === 'textarea') {
      return (
        <AntInput.TextArea
          ref={ref as any}
          className={cn(
            "border-input placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex w-full min-w-0 rounded-md border px-3 py-2 text-base shadow-xs transition-all outline-none disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-[#444] dark:border-[#666] dark:text-white",
            className
          )}
          style={{
            backgroundColor: 'var(--input-bg, #444)',
            color: 'var(--input-text, white)'
          }}
          {...props}
        />
      );
    }

    // For regular input
    return (
      <AntInput
        ref={ref as any}
        type={type}
        className={cn(baseInputClasses, className)}
        style={{
          backgroundColor: 'var(--input-bg, #444)',
          color: 'var(--input-text, white)'
        }}
        {...props}
      />
    );
  }
);

Input.displayName = 'Input';

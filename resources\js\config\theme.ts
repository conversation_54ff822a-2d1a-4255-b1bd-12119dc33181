import { ThemeEditorState, ThemeStyleProps } from "../types/theme";

export const defaultLightThemeStyles: ThemeStyleProps = {
  background: "#ffffff",
  foreground: "#333333",
  card: "#ffffff",
  "card-foreground": "#333333",
  popover: "#ffffff",
  "popover-foreground": "#333333",
  primary: "#3b82f6",
  "primary-foreground": "#ffffff",
  secondary: "#f3f4f6",
  "secondary-foreground": "#4b5563",
  muted: "#f9fafb",
  "muted-foreground": "#6b7280",
  accent: "#e0f2fe",
  "accent-foreground": "#1e3a8a",
  destructive: "#ef4444",
  "destructive-foreground": "#ffffff",
  border: "#e5e7eb",
  input: "#e5e7eb",
  ring: "#3b82f6",
  radius: "0.375rem",
  "font-sans": "Inter, sans-serif",
  "font-serif": "Georgia, serif",
  "font-mono": "Menlo, monospace",
};

export const defaultDarkThemeStyles: ThemeStyleProps = {
  background: "#171717",
  foreground: "#e5e5e5",
  card: "#262626",
  "card-foreground": "#e5e5e5",
  popover: "#262626",
  "popover-foreground": "#e5e5e5",
  primary: "#3b82f6",
  "primary-foreground": "#ffffff",
  secondary: "#262626",
  "secondary-foreground": "#e5e5e5",
  muted: "#262626",
  "muted-foreground": "#a3a3a3",
  accent: "#1e3a8a",
  "accent-foreground": "#bfdbfe",
  destructive: "#ef4444",
  "destructive-foreground": "#ffffff",
  border: "#404040",
  input: "#404040",
  ring: "#3b82f6",
  radius: "0.375rem",
  "font-sans": "Inter, sans-serif",
  "font-serif": "Georgia, serif",
  "font-mono": "Menlo, monospace",
};

export const defaultThemeState: ThemeEditorState = {
  styles: {
    light: defaultLightThemeStyles,
    dark: defaultDarkThemeStyles,
  },
  currentMode: 
    typeof window !== "undefined" && window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light",
};

// Common styles that should be applied to both light and dark modes
export const COMMON_STYLES = [
  "radius",
  "font-sans",
  "font-serif",
  "font-mono",
];

import { usePage } from '@inertiajs/react';

interface TranslationData {
    [key: string]: string | TranslationData;
}

interface PageProps {
    translations?: TranslationData;
    locale?: string;
    [key: string]: any;
}

/**
 * Custom hook for handling translations in React components
 */
export function useTranslation() {
    const { props } = usePage<PageProps>();
    const translations = props.translations || {};
    const currentLocale = props.locale || 'en';

    /**
     * Get a translation by key with optional parameters
     * @param key - Translation key (e.g., 'common.save' or 'school.settings_updated')
     * @param params - Optional parameters to replace in the translation
     * @param fallback - Fallback text if translation is not found
     */
    const t = (key: string, params?: Record<string, string | number>, fallback?: string): string => {
        const keys = key.split('.');
        let value: any = translations;

        // Navigate through the nested translation object
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                // Translation not found, return fallback or key
                return fallback || key;
            }
        }

        // If we found a string value, process parameters
        if (typeof value === 'string') {
            let result = value;
            
            // Replace parameters in the format :param
            if (params) {
                Object.entries(params).forEach(([paramKey, paramValue]) => {
                    result = result.replace(new RegExp(`:${paramKey}`, 'g'), String(paramValue));
                });
            }
            
            return result;
        }

        // If value is not a string, return fallback or key
        return fallback || key;
    };

    /**
     * Get the current locale
     */
    const locale = (): string => {
        return currentLocale;
    };

    /**
     * Check if current locale is RTL
     */
    const isRTL = (): boolean => {
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        return rtlLanguages.includes(currentLocale);
    };

    /**
     * Get text direction for current locale
     */
    const direction = (): 'ltr' | 'rtl' => {
        return isRTL() ? 'rtl' : 'ltr';
    };

    /**
     * Format a number according to current locale
     */
    const formatNumber = (number: number, options?: Intl.NumberFormatOptions): string => {
        return new Intl.NumberFormat(currentLocale, options).format(number);
    };

    /**
     * Format a date according to current locale
     */
    const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return new Intl.DateTimeFormat(currentLocale, options).format(dateObj);
    };

    /**
     * Format currency according to current locale
     */
    const formatCurrency = (amount: number, currency: string = 'TND'): string => {
        return new Intl.NumberFormat(currentLocale, {
            style: 'currency',
            currency: currency,
        }).format(amount);
    };

    /**
     * Get localized month names
     */
    const getMonthNames = (format: 'long' | 'short' = 'long'): string[] => {
        const months = [];
        for (let i = 0; i < 12; i++) {
            const date = new Date(2024, i, 1);
            months.push(new Intl.DateTimeFormat(currentLocale, { month: format }).format(date));
        }
        return months;
    };

    /**
     * Get localized day names
     */
    const getDayNames = (format: 'long' | 'short' = 'long'): string[] => {
        const days = [];
        // Start from Sunday (0) to Saturday (6)
        for (let i = 0; i < 7; i++) {
            const date = new Date(2024, 0, i + 7); // January 7, 2024 is a Sunday
            days.push(new Intl.DateTimeFormat(currentLocale, { weekday: format }).format(date));
        }
        return days;
    };

    /**
     * Pluralize a translation key based on count
     * Looks for keys like 'item' and 'items' or 'item_one' and 'item_other'
     */
    const pluralize = (key: string, count: number, params?: Record<string, string | number>): string => {
        const pluralKey = count === 1 ? `${key}_one` : `${key}_other`;
        
        // Try plural-specific key first
        const pluralTranslation = t(pluralKey, { ...params, count }, null);
        if (pluralTranslation !== pluralKey) {
            return pluralTranslation;
        }
        
        // Fallback to simple plural form
        const simpleKey = count === 1 ? key : `${key}s`;
        return t(simpleKey, { ...params, count });
    };

    return {
        t,
        locale,
        isRTL,
        direction,
        formatNumber,
        formatDate,
        formatCurrency,
        getMonthNames,
        getDayNames,
        pluralize,
    };
}

/**
 * Simple translation function for use outside of React components
 * Note: This requires translations to be available globally
 */
export function trans(key: string, params?: Record<string, string | number>, fallback?: string): string {
    // This would need to be implemented based on how you want to handle
    // translations outside of React components (e.g., using a global store)
    return fallback || key;
}

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Plus, Calendar, Users, CheckCircle, Clock } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface AcademicYear {
    id: number;
    name: string;
    name_ar?: string;
    name_fr?: string;
    description?: string;
    start_date: string;
    end_date: string;
    is_active: boolean;
    is_current: boolean;
    periods_count?: number;
    periods?: Period[];
}

interface Period {
    id: number;
    name: string;
    order: number;
    type: string;
    start_date: string;
    end_date: string;
    is_active: boolean;
    is_current: boolean;
}

interface School {
    id: number;
    name: string;
    localized_name: string;
}

interface IndexProps {
    academicYears: AcademicYear[];
    school: School;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Administration', href: '#' },
    { title: 'Academic Years', href: '/admin/academic-years' },
];

export default function Index({ academicYears, school }: IndexProps) {
    const { t } = useTranslation();

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const getStatusBadge = (academicYear: AcademicYear) => {
        if (academicYear.is_current) {
            return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Current</Badge>;
        }
        if (academicYear.is_active) {
            return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Active</Badge>;
        }
        return <Badge variant="outline">Inactive</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Academic Years" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Academic Years</h1>
                        <p className="text-muted-foreground">
                            Manage academic years for {school.localized_name}
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/admin/academic-years/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Academic Year
                        </Link>
                    </Button>
                </div>

                {/* Academic Years Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {academicYears.map((academicYear) => (
                        <Card key={academicYear.id} className="hover:shadow-md transition-shadow">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg">{academicYear.name}</CardTitle>
                                    {getStatusBadge(academicYear)}
                                </div>
                                <CardDescription>
                                    {academicYear.description || 'No description provided'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Calendar className="mr-2 h-4 w-4" />
                                        {formatDate(academicYear.start_date)} - {formatDate(academicYear.end_date)}
                                    </div>
                                    
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Users className="mr-2 h-4 w-4" />
                                        {academicYear.periods?.length || 0} periods
                                    </div>

                                    <div className="flex gap-2 pt-2">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/academic-years/${academicYear.id}`}>
                                                View
                                            </Link>
                                        </Button>
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/academic-years/${academicYear.id}/edit`}>
                                                Edit
                                            </Link>
                                        </Button>
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/periods?academic_year_id=${academicYear.id}`}>
                                                Periods
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {academicYears.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Academic Years</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Get started by creating your first academic year.
                            </p>
                            <Button asChild>
                                <Link href="/admin/academic-years/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Academic Year
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}

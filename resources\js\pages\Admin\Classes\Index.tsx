import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Plus, Users, MapPin, CheckCircle, XCircle, Filter } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface SchoolClass {
    id: number;
    name: string;
    name_ar?: string;
    name_fr?: string;
    description?: string;
    grade_level: string;
    section?: string;
    capacity: number;
    classroom?: string;
    is_active: boolean;
    academic_year: {
        id: number;
        name: string;
        localized_name: string;
    };
    full_name: string;
    current_enrollment_count?: number;
}

interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
}

interface School {
    id: number;
    name: string;
    localized_name: string;
}

interface IndexProps {
    classes: SchoolClass[];
    academicYears: AcademicYear[];
    gradeLevels: string[];
    filters: {
        academic_year_id?: string;
        grade_level?: string;
    };
    school: School;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Administration', href: '#' },
    { title: 'Classes', href: '/admin/classes' },
];

export default function Index({ classes, academicYears, gradeLevels, filters, school }: IndexProps) {
    const { t } = useTranslation();

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filters };
        if (value === 'all') {
            delete newFilters[key as keyof typeof filters];
        } else {
            newFilters[key as keyof typeof filters] = value;
        }
        
        router.get('/admin/classes', newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusBadge = (schoolClass: SchoolClass) => {
        if (schoolClass.is_active) {
            return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
        }
        return <Badge variant="secondary"><XCircle className="w-3 h-3 mr-1" />Inactive</Badge>;
    };

    const getCapacityInfo = (schoolClass: SchoolClass) => {
        const enrolled = schoolClass.current_enrollment_count || 0;
        const available = schoolClass.capacity - enrolled;
        const percentage = (enrolled / schoolClass.capacity) * 100;
        
        return {
            enrolled,
            available,
            percentage,
            isFull: enrolled >= schoolClass.capacity,
        };
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Classes" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Classes</h1>
                        <p className="text-muted-foreground">
                            Manage classes for {school.localized_name}
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/admin/classes/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Class
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Filter className="mr-2 h-4 w-4" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div>
                                <label className="text-sm font-medium mb-2 block">Academic Year</label>
                                <Select
                                    value={filters.academic_year_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('academic_year_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Academic Years" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Academic Years</SelectItem>
                                        {academicYears.map((year) => (
                                            <SelectItem key={year.id} value={year.id.toString()}>
                                                {year.localized_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Grade Level</label>
                                <Select
                                    value={filters.grade_level || 'all'}
                                    onValueChange={(value) => handleFilterChange('grade_level', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Grade Levels" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Grade Levels</SelectItem>
                                        {gradeLevels.map((level) => (
                                            <SelectItem key={level} value={level}>
                                                {level}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Classes Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {classes.map((schoolClass) => {
                        const capacityInfo = getCapacityInfo(schoolClass);
                        
                        return (
                            <Card key={schoolClass.id} className="hover:shadow-md transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-lg">{schoolClass.name}</CardTitle>
                                        {getStatusBadge(schoolClass)}
                                    </div>
                                    <CardDescription>
                                        {schoolClass.grade_level}
                                        {schoolClass.section && ` - Section ${schoolClass.section}`}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Users className="mr-2 h-4 w-4" />
                                            {capacityInfo.enrolled}/{schoolClass.capacity} students
                                            <Badge 
                                                variant={capacityInfo.isFull ? "destructive" : "secondary"} 
                                                className="ml-2"
                                            >
                                                {Math.round(capacityInfo.percentage)}%
                                            </Badge>
                                        </div>
                                        
                                        {schoolClass.classroom && (
                                            <div className="flex items-center text-sm text-muted-foreground">
                                                <MapPin className="mr-2 h-4 w-4" />
                                                Room {schoolClass.classroom}
                                            </div>
                                        )}

                                        <div className="text-sm text-muted-foreground">
                                            Academic Year: {schoolClass.academic_year.localized_name}
                                        </div>

                                        <div className="flex gap-2 pt-2">
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={`/admin/classes/${schoolClass.id}`}>
                                                    View
                                                </Link>
                                            </Button>
                                            <Button variant="outline" size="sm" asChild>
                                                <Link href={`/admin/classes/${schoolClass.id}/edit`}>
                                                    Edit
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {classes.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Users className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Classes Found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                {filters.academic_year_id || filters.grade_level 
                                    ? 'No classes match your current filters.' 
                                    : 'Get started by creating your first class.'
                                }
                            </p>
                            <Button asChild>
                                <Link href="/admin/classes/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Class
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}

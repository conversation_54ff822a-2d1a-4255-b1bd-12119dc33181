import { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import Heading from '@/components/heading';
import InputError from '@/components/input-error';
import { Upload, X } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface School {
    id?: number;
    name: string;
    name_ar?: string;
    name_fr?: string;
    description?: string;
    description_ar?: string;
    description_fr?: string;
    email?: string;
    phone?: string;
    fax?: string;
    website?: string;
    address: string;
    address_ar?: string;
    address_fr?: string;
    city: string;
    state_province?: string;
    postal_code?: string;
    country: string;
    academic_year_start_month: string;
    academic_year_end_month: string;
    terms_per_year: number;
    default_language: string;
    supported_languages: string[];
    timezone: string;
    currency: string;
    logo_path?: string;
    is_active: boolean;
}

interface Props {
    school?: School;
    supportedLanguages: Array<{ value: string; label: string }>;
    months: Array<{ value: string; label: string }>;
    timezones: Array<{ value: string; label: string }>;
    currencies: Array<{ value: string; label: string }>;
}

export default function SchoolSettings({ school, supportedLanguages, months, timezones, currencies }: Props) {
    const { t } = useTranslation();
    const [logoPreview, setLogoPreview] = useState<string | null>(
        school?.logo_path ? `/storage/${school.logo_path}` : null
    );

    const { data, setData, post, processing, errors, reset } = useForm({
        name: school?.name || '',
        name_ar: school?.name_ar || '',
        name_fr: school?.name_fr || '',
        description: school?.description || '',
        description_ar: school?.description_ar || '',
        description_fr: school?.description_fr || '',
        email: school?.email || '',
        phone: school?.phone || '',
        fax: school?.fax || '',
        website: school?.website || '',
        address: school?.address || '',
        address_ar: school?.address_ar || '',
        address_fr: school?.address_fr || '',
        city: school?.city || '',
        state_province: school?.state_province || '',
        postal_code: school?.postal_code || '',
        country: school?.country || 'Tunisia',
        academic_year_start_month: school?.academic_year_start_month || 'September',
        academic_year_end_month: school?.academic_year_end_month || 'June',
        terms_per_year: school?.terms_per_year || 3,
        default_language: school?.default_language || 'ar',
        supported_languages: school?.supported_languages || ['ar', 'fr'],
        timezone: school?.timezone || 'Africa/Tunis',
        currency: school?.currency || 'TND',
        logo: null as File | null,
        is_active: school?.is_active ?? true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.school-settings.store'), {
            forceFormData: true,
        });
    };

    const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('logo', file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setLogoPreview(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const removeLogo = () => {
        if (school?.logo_path) {
            // Call API to remove logo
            // For now, just clear the preview
        }
        setLogoPreview(null);
        setData('logo', null);
    };

    const handleLanguageChange = (language: string, checked: boolean) => {
        if (checked) {
            setData('supported_languages', [...data.supported_languages, language]);
        } else {
            setData('supported_languages', data.supported_languages.filter(lang => lang !== language));
        }
    };

    return (
        <AppLayout breadcrumbs={[
            { title: t('common.dashboard', {}, 'Dashboard'), href: '/dashboard' },
            { title: t('common.admin', {}, 'Admin'), href: '#' },
            { title: t('school.school_settings'), href: '/admin/school-settings' }
        ]}>
            <Head title={t('school.school_settings')} />

            <div className="space-y-6 p-6">
                <Heading title={t('school.school_settings')} description={t('school.configure_basic_info')} />

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>{t('school.basic_information')}</CardTitle>
                            <CardDescription>
                                {t('school.configure_basic_info')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="name">{t('school.school_name_default')}</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        required
                                    />
                                    <InputError message={errors.name} />
                                </div>
                                <div>
                                    <Label htmlFor="name_ar">{t('school.school_name_arabic')}</Label>
                                    <Input
                                        id="name_ar"
                                        value={data.name_ar}
                                        onChange={(e) => setData('name_ar', e.target.value)}
                                        dir="rtl"
                                    />
                                    <InputError message={errors.name_ar} />
                                </div>
                                <div>
                                    <Label htmlFor="name_fr">{t('school.school_name_french')}</Label>
                                    <Input
                                        id="name_fr"
                                        value={data.name_fr}
                                        onChange={(e) => setData('name_fr', e.target.value)}
                                    />
                                    <InputError message={errors.name_fr} />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="description">{t('school.description_default')}</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        rows={3}
                                    />
                                    <InputError message={errors.description} />
                                </div>
                                <div>
                                    <Label htmlFor="description_ar">{t('school.description_arabic')}</Label>
                                    <Textarea
                                        id="description_ar"
                                        value={data.description_ar}
                                        onChange={(e) => setData('description_ar', e.target.value)}
                                        rows={3}
                                        dir="rtl"
                                    />
                                    <InputError message={errors.description_ar} />
                                </div>
                                <div>
                                    <Label htmlFor="description_fr">{t('school.description_french')}</Label>
                                    <Textarea
                                        id="description_fr"
                                        value={data.description_fr}
                                        onChange={(e) => setData('description_fr', e.target.value)}
                                        rows={3}
                                    />
                                    <InputError message={errors.description_fr} />
                                </div>
                            </div>

                            <Separator />

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="email">{t('school.school_email')}</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                    />
                                    <InputError message={errors.email} />
                                </div>
                                <div>
                                    <Label htmlFor="phone">{t('school.school_phone')}</Label>
                                    <Input
                                        id="phone"
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                    />
                                    <InputError message={errors.phone} />
                                </div>
                                <div>
                                    <Label htmlFor="website">{t('school.school_website')}</Label>
                                    <Input
                                        id="website"
                                        type="url"
                                        value={data.website}
                                        onChange={(e) => setData('website', e.target.value)}
                                    />
                                    <InputError message={errors.website} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Logo Upload */}
                    <Card>
                        <CardHeader>
                            <CardTitle>{t('school.school_logo')}</CardTitle>
                            <CardDescription>
                                {t('school.logo_recommendation')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-4">
                                {logoPreview && (
                                    <div className="relative">
                                        <img
                                            src={logoPreview}
                                            alt={t('school.school_logo')}
                                            className="w-20 h-20 object-cover rounded-lg border"
                                        />
                                        <Button
                                            type="button"
                                            variant="destructive"
                                            size="sm"
                                            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                            onClick={removeLogo}
                                        >
                                            <X className="h-3 w-3" />
                                        </Button>
                                    </div>
                                )}
                                <div>
                                    <Label htmlFor="logo" className="cursor-pointer">
                                        <div className="flex items-center space-x-2 px-4 py-2 border border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                                            <Upload className="h-4 w-4" />
                                            <span>{t('school.upload_logo')}</span>
                                        </div>
                                    </Label>
                                    <Input
                                        id="logo"
                                        type="file"
                                        accept="image/*"
                                        onChange={handleLogoChange}
                                        className="hidden"
                                    />
                                    <InputError message={errors.logo} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Address Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>{t('school.address_information')}</CardTitle>
                            <CardDescription>
                                {t('school.configure_address')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="address">{t('school.address_default')}</Label>
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        required
                                        rows={3}
                                    />
                                    <InputError message={errors.address} />
                                </div>
                                <div>
                                    <Label htmlFor="address_ar">{t('school.address_arabic')}</Label>
                                    <Textarea
                                        id="address_ar"
                                        value={data.address_ar}
                                        onChange={(e) => setData('address_ar', e.target.value)}
                                        rows={3}
                                        dir="rtl"
                                    />
                                    <InputError message={errors.address_ar} />
                                </div>
                                <div>
                                    <Label htmlFor="address_fr">{t('school.address_french')}</Label>
                                    <Textarea
                                        id="address_fr"
                                        value={data.address_fr}
                                        onChange={(e) => setData('address_fr', e.target.value)}
                                        rows={3}
                                    />
                                    <InputError message={errors.address_fr} />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <Label htmlFor="city">{t('common.city', {}, 'City')}</Label>
                                    <Input
                                        id="city"
                                        value={data.city}
                                        onChange={(e) => setData('city', e.target.value)}
                                        required
                                    />
                                    <InputError message={errors.city} />
                                </div>
                                <div>
                                    <Label htmlFor="state_province">{t('school.state_province')}</Label>
                                    <Input
                                        id="state_province"
                                        value={data.state_province}
                                        onChange={(e) => setData('state_province', e.target.value)}
                                    />
                                    <InputError message={errors.state_province} />
                                </div>
                                <div>
                                    <Label htmlFor="postal_code">{t('school.postal_code')}</Label>
                                    <Input
                                        id="postal_code"
                                        value={data.postal_code}
                                        onChange={(e) => setData('postal_code', e.target.value)}
                                    />
                                    <InputError message={errors.postal_code} />
                                </div>
                                <div>
                                    <Label htmlFor="country">{t('common.country', {}, 'Country')}</Label>
                                    <Input
                                        id="country"
                                        value={data.country}
                                        onChange={(e) => setData('country', e.target.value)}
                                        required
                                    />
                                    <InputError message={errors.country} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Academic Settings */}
                    <Card>
                        <CardHeader>
                            <CardTitle>{t('school.academic_settings')}</CardTitle>
                            <CardDescription>
                                {t('school.configure_academic')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="academic_year_start_month">{t('school.academic_year_start')}</Label>
                                    <Select value={data.academic_year_start_month} onValueChange={(value) => setData('academic_year_start_month', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {months.map((month) => (
                                                <SelectItem key={month.value} value={month.value}>
                                                    {month.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.academic_year_start_month} />
                                </div>
                                <div>
                                    <Label htmlFor="academic_year_end_month">{t('school.academic_year_end')}</Label>
                                    <Select value={data.academic_year_end_month} onValueChange={(value) => setData('academic_year_end_month', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {months.map((month) => (
                                                <SelectItem key={month.value} value={month.value}>
                                                    {month.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.academic_year_end_month} />
                                </div>
                                <div>
                                    <Label htmlFor="terms_per_year">{t('school.terms_per_year')}</Label>
                                    <Select value={data.terms_per_year.toString()} onValueChange={(value) => setData('terms_per_year', parseInt(value))}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="1">{t('school.one_term')}</SelectItem>
                                            <SelectItem value="2">{t('school.two_terms')}</SelectItem>
                                            <SelectItem value="3">{t('school.three_terms')}</SelectItem>
                                            <SelectItem value="4">{t('school.four_terms')}</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.terms_per_year} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Language & System Settings */}
                    <Card>
                        <CardHeader>
                            <CardTitle>{t('school.language_system_settings')}</CardTitle>
                            <CardDescription>
                                {t('school.configure_language_system')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label>{t('school.supported_languages')}</Label>
                                <div className="flex flex-wrap gap-4 mt-2">
                                    {supportedLanguages.map((language) => (
                                        <div key={language.value} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={`lang-${language.value}`}
                                                checked={data.supported_languages.includes(language.value)}
                                                onCheckedChange={(checked) =>
                                                    handleLanguageChange(language.value, checked as boolean)
                                                }
                                            />
                                            <Label htmlFor={`lang-${language.value}`}>
                                                {language.label}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                                <InputError message={errors.supported_languages} />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="default_language">{t('school.default_language')}</Label>
                                    <Select value={data.default_language} onValueChange={(value) => setData('default_language', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {supportedLanguages.map((language) => (
                                                <SelectItem key={language.value} value={language.value}>
                                                    {language.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.default_language} />
                                </div>
                                <div>
                                    <Label htmlFor="timezone">{t('school.timezone')}</Label>
                                    <Select value={data.timezone} onValueChange={(value) => setData('timezone', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {timezones.map((timezone) => (
                                                <SelectItem key={timezone.value} value={timezone.value}>
                                                    {timezone.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.timezone} />
                                </div>
                                <div>
                                    <Label htmlFor="currency">{t('school.currency')}</Label>
                                    <Select value={data.currency} onValueChange={(value) => setData('currency', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {currencies.map((currency) => (
                                                <SelectItem key={currency.value} value={currency.value}>
                                                    {currency.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.currency} />
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked as boolean)}
                                />
                                <Label htmlFor="is_active">
                                    {t('school.school_is_active')}
                                </Label>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Save Button */}
                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing}>
                            {processing ? t('common.saving', {}, 'Saving...') : t('common.save_settings', {}, 'Save Settings')}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}

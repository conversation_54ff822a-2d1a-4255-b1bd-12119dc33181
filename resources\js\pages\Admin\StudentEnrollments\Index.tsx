import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Plus, UserPlus, Users, Building2, Calendar, CheckCircle, XCircle, Filter, BarChart3, AlertTriangle, Repeat } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface Student {
    id: number;
    name: string;
    email: string;
}

interface SchoolClass {
    id: number;
    name: string;
    grade_level: string;
    section?: string;
    capacity: number;
}

interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
}

interface StudentEnrollment {
    id: number;
    student: Student;
    school_class: SchoolClass;
    academic_year: AcademicYear;
    student_number: string;
    enrollment_date: string;
    withdrawal_date?: string;
    status: 'enrolled' | 'withdrawn' | 'transferred' | 'graduated';
    status_label: string;
    is_repeating: boolean;
    previous_school?: string;
    notes?: string;
    display_name: string;
    is_active: boolean;
}

interface School {
    id: number;
    name: string;
    localized_name: string;
}

interface EnrollmentStats {
    total: number;
    enrolled: number;
    withdrawn: number;
    transferred: number;
    graduated: number;
    repeating: number;
}

interface IndexProps {
    enrollments: StudentEnrollment[];
    academicYears: AcademicYear[];
    classes: SchoolClass[];
    statuses: Record<string, string>;
    stats: EnrollmentStats;
    filters: {
        academic_year_id?: string;
        class_id?: string;
        status?: string;
    };
    school: School;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Administration', href: '#' },
    { title: 'Student Enrollments', href: '/admin/student-enrollments' },
];

export default function Index({ enrollments, academicYears, classes, statuses, stats, filters, school }: IndexProps) {
    const { t } = useTranslation();

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filters };
        if (value === 'all') {
            delete newFilters[key as keyof typeof filters];
        } else {
            newFilters[key as keyof typeof filters] = value;
        }
        
        router.get('/admin/student-enrollments', newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusBadge = (enrollment: StudentEnrollment) => {
        const statusColors = {
            'Enrolled': 'bg-green-500',
            'Withdrawn': 'bg-red-500',
            'Transferred': 'bg-blue-500',
            'Graduated': 'bg-purple-500',
        };
        
        const color = statusColors[enrollment.status_label as keyof typeof statusColors] || 'bg-gray-500';
        
        return (
            <Badge variant="default" className={color}>
                {enrollment.status_label === 'Enrolled' && <CheckCircle className="w-3 h-3 mr-1" />}
                {enrollment.status_label === 'Withdrawn' && <XCircle className="w-3 h-3 mr-1" />}
                {enrollment.status_label}
            </Badge>
        );
    };

    const getClassCapacityInfo = (schoolClass: SchoolClass) => {
        const currentEnrollments = enrollments.filter(e => 
            e.school_class.id === schoolClass.id && e.status === 'enrolled'
        ).length;
        const percentage = (currentEnrollments / schoolClass.capacity) * 100;
        
        return {
            current: currentEnrollments,
            capacity: schoolClass.capacity,
            percentage: Math.round(percentage),
            isFull: currentEnrollments >= schoolClass.capacity,
        };
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Student Enrollments" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Student Enrollments</h1>
                        <p className="text-muted-foreground">
                            Manage student enrollments and class assignments for {school.localized_name}
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/admin/student-enrollments/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Enrollment
                        </Link>
                    </Button>
                </div>

                {/* Statistics Cards */}
                <div className="grid gap-4 md:grid-cols-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Enrolled</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.enrolled}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Withdrawn</CardTitle>
                            <XCircle className="h-4 w-4 text-red-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{stats.withdrawn}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Transferred</CardTitle>
                            <BarChart3 className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">{stats.transferred}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Graduated</CardTitle>
                            <CheckCircle className="h-4 w-4 text-purple-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-600">{stats.graduated}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Repeating</CardTitle>
                            <Repeat className="h-4 w-4 text-orange-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">{stats.repeating}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Filter className="mr-2 h-4 w-4" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div>
                                <label className="text-sm font-medium mb-2 block">Academic Year</label>
                                <Select
                                    value={filters.academic_year_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('academic_year_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Academic Years" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Academic Years</SelectItem>
                                        {academicYears.map((year) => (
                                            <SelectItem key={year.id} value={year.id.toString()}>
                                                {year.localized_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Class</label>
                                <Select
                                    value={filters.class_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('class_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Classes" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Classes</SelectItem>
                                        {classes.map((schoolClass) => {
                                            const capacityInfo = getClassCapacityInfo(schoolClass);
                                            return (
                                                <SelectItem key={schoolClass.id} value={schoolClass.id.toString()}>
                                                    {schoolClass.name} ({capacityInfo.current}/{capacityInfo.capacity})
                                                </SelectItem>
                                            );
                                        })}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Status</label>
                                <Select
                                    value={filters.status || 'all'}
                                    onValueChange={(value) => handleFilterChange('status', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Statuses" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Statuses</SelectItem>
                                        {Object.entries(statuses).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Enrollments Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {enrollments.map((enrollment) => (
                        <Card key={enrollment.id} className="hover:shadow-md transition-shadow">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg flex items-center">
                                        <Users className="mr-2 h-4 w-4" />
                                        {enrollment.student.name}
                                        {enrollment.is_repeating && <Repeat className="ml-2 h-4 w-4 text-orange-500" />}
                                    </CardTitle>
                                    {getStatusBadge(enrollment)}
                                </div>
                                <CardDescription>
                                    <div className="space-y-1">
                                        <div className="flex items-center">
                                            <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                                                {enrollment.student_number}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <Building2 className="mr-1 h-3 w-3" />
                                            {enrollment.school_class.name}
                                        </div>
                                    </div>
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Calendar className="mr-2 h-4 w-4" />
                                        {enrollment.academic_year.localized_name}
                                    </div>
                                    
                                    <div className="text-sm text-muted-foreground">
                                        Enrolled: {new Date(enrollment.enrollment_date).toLocaleDateString()}
                                        {enrollment.withdrawal_date && (
                                            <div>Withdrawn: {new Date(enrollment.withdrawal_date).toLocaleDateString()}</div>
                                        )}
                                    </div>
                                    
                                    {enrollment.is_repeating && (
                                        <Badge variant="outline" className="text-orange-600 border-orange-600">
                                            <Repeat className="mr-1 h-3 w-3" />
                                            Repeating Grade
                                        </Badge>
                                    )}
                                    
                                    {enrollment.previous_school && (
                                        <div className="text-sm text-muted-foreground">
                                            <AlertTriangle className="inline mr-1 h-3 w-3" />
                                            Transfer from: {enrollment.previous_school}
                                        </div>
                                    )}
                                    
                                    {enrollment.notes && (
                                        <div className="text-sm text-muted-foreground">
                                            <p className="truncate">{enrollment.notes}</p>
                                        </div>
                                    )}

                                    <div className="flex gap-2 pt-2">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/student-enrollments/${enrollment.id}`}>
                                                View
                                            </Link>
                                        </Button>
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/student-enrollments/${enrollment.id}/edit`}>
                                                Edit
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {enrollments.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <UserPlus className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Student Enrollments Found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                {Object.values(filters).some(Boolean)
                                    ? 'No enrollments match your current filters.' 
                                    : 'Get started by creating your first student enrollment.'
                                }
                            </p>
                            <Button asChild>
                                <Link href="/admin/student-enrollments/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Enrollment
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}

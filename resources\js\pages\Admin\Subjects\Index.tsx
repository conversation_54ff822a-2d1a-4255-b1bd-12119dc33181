import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Plus, Book, Clock, Award, CheckCircle, XCircle, Filter, GraduationCap } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface Subject {
    id: number;
    name: string;
    name_ar?: string;
    name_fr?: string;
    code?: string;
    description?: string;
    grade_levels?: string[];
    coefficient: number;
    weekly_hours: number;
    category: 'core' | 'elective' | 'extracurricular';
    category_label: string;
    is_active: boolean;
    formatted_grade_levels: string;
    localized_name: string;
    localized_description?: string;
}

interface School {
    id: number;
    name: string;
    localized_name: string;
}

interface IndexProps {
    subjects: Subject[];
    categories: Record<string, string>;
    gradeLevels: string[];
    filters: {
        category?: string;
        grade_level?: string;
    };
    school: School;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Administration', href: '#' },
    { title: 'Subjects', href: '/admin/subjects' },
];

export default function Index({ subjects, categories, gradeLevels, filters, school }: IndexProps) {
    const { t } = useTranslation();

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filters };
        if (value === 'all') {
            delete newFilters[key as keyof typeof filters];
        } else {
            newFilters[key as keyof typeof filters] = value;
        }
        
        router.get('/admin/subjects', newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusBadge = (subject: Subject) => {
        if (subject.is_active) {
            return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
        }
        return <Badge variant="secondary"><XCircle className="w-3 h-3 mr-1" />Inactive</Badge>;
    };

    const getCategoryBadge = (category: string) => {
        const variants = {
            core: 'default',
            elective: 'secondary',
            extracurricular: 'outline',
        } as const;
        
        return <Badge variant={variants[category as keyof typeof variants] || 'outline'}>{categories[category]}</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Subjects" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Subjects</h1>
                        <p className="text-muted-foreground">
                            Manage academic subjects for {school.localized_name}
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/admin/subjects/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Subject
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Filter className="mr-2 h-4 w-4" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div>
                                <label className="text-sm font-medium mb-2 block">Category</label>
                                <Select
                                    value={filters.category || 'all'}
                                    onValueChange={(value) => handleFilterChange('category', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Categories" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Categories</SelectItem>
                                        {Object.entries(categories).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Grade Level</label>
                                <Select
                                    value={filters.grade_level || 'all'}
                                    onValueChange={(value) => handleFilterChange('grade_level', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Grade Levels" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Grade Levels</SelectItem>
                                        {gradeLevels.map((level) => (
                                            <SelectItem key={level} value={level}>
                                                {level}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Subjects Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {subjects.map((subject) => (
                        <Card key={subject.id} className="hover:shadow-md transition-shadow">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg">{subject.localized_name}</CardTitle>
                                    {getStatusBadge(subject)}
                                </div>
                                <CardDescription>
                                    {subject.code && <span className="font-mono text-xs bg-muted px-2 py-1 rounded">{subject.code}</span>}
                                    {subject.localized_description && (
                                        <span className="block mt-1">{subject.localized_description}</span>
                                    )}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        {getCategoryBadge(subject.category)}
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Award className="mr-1 h-3 w-3" />
                                            Coeff: {subject.coefficient}
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Clock className="mr-2 h-4 w-4" />
                                        {subject.weekly_hours} hours/week
                                    </div>
                                    
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <GraduationCap className="mr-2 h-4 w-4" />
                                        {subject.formatted_grade_levels}
                                    </div>

                                    <div className="flex gap-2 pt-2">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/subjects/${subject.id}`}>
                                                View
                                            </Link>
                                        </Button>
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/admin/subjects/${subject.id}/edit`}>
                                                Edit
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {subjects.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Book className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Subjects Found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                {filters.category || filters.grade_level 
                                    ? 'No subjects match your current filters.' 
                                    : 'Get started by creating your first subject.'
                                }
                            </p>
                            <Button asChild>
                                <Link href="/admin/subjects/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Subject
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}

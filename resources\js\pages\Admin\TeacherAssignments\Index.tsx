import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DataTable, DataTableColumnHeader } from '@/components/ui/data-table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Plus, Users, Building2, Book, CheckCircle, XCircle, Calendar, Star, MoreHorizontal, Eye, Edit } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import { ColumnDef } from '@tanstack/react-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';


interface Teacher {
    id: number;
    name: string;
    email: string;
}

interface SchoolClass {
    id: number;
    name: string;
    grade_level: string;
    section?: string;
}

interface Subject {
    id: number;
    name: string;
    localized_name: string;
    code?: string;
    category: string;
}

interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
}

interface TeacherAssignment {
    id: number;
    teacher: Teacher;
    school_class: SchoolClass;
    subject: Subject;
    academic_year: AcademicYear;
    is_primary: boolean;
    is_active: boolean;
    start_date?: string;
    end_date?: string;
    notes?: string;
    display_name: string;
    status_label: string;
}

interface School {
    id: number;
    name: string;
    localized_name: string;
}

interface IndexProps {
    assignments: TeacherAssignment[];
    academicYears: AcademicYear[];
    teachers: Teacher[];
    classes: SchoolClass[];
    subjects: Subject[];
    filters: {
        academic_year_id?: string;
        teacher_id?: string;
        class_id?: string;
        subject_id?: string;
    };
    school: School;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Administration', href: '#' },
    { title: 'Teacher Assignments', href: '/admin/teacher-assignments' },
];

export default function Index({ assignments, filters, school }: IndexProps) {
    const { t } = useTranslation();

    // Define table columns
    const columns: ColumnDef<TeacherAssignment>[] = [
        {
            accessorKey: "teacher.name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('common.teacher')} />
            ),
            cell: ({ row }) => {
                const assignment = row.original;
                return (
                    <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{assignment.teacher.name}</span>
                        {assignment.is_primary && (
                            <Star className="h-4 w-4 text-yellow-500" />
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: "school_class.name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('common.class')} />
            ),
            cell: ({ row }) => {
                const assignment = row.original;
                return (
                    <div className="flex items-center space-x-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span>{assignment.school_class.name}</span>
                    </div>
                );
            },
        },
        {
            accessorKey: "subject.localized_name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('common.subject')} />
            ),
            cell: ({ row }) => {
                const assignment = row.original;
                return (
                    <div className="flex items-center space-x-2">
                        <Book className="h-4 w-4 text-muted-foreground" />
                        <div className="flex flex-col">
                            <span>{assignment.subject.localized_name}</span>
                            {assignment.subject.code && (
                                <span className="text-xs text-muted-foreground font-mono">
                                    {assignment.subject.code}
                                </span>
                            )}
                        </div>
                    </div>
                );
            },
        },
        {
            accessorKey: "subject.category",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('common.category')} />
            ),
            cell: ({ row }) => {
                const assignment = row.original;
                return getCategoryBadge(assignment.subject.category);
            },
        },
        {
            accessorKey: "academic_year.localized_name",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('common.academic_year')} />
            ),
            cell: ({ row }) => {
                const assignment = row.original;
                return (
                    <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{assignment.academic_year.localized_name}</span>
                    </div>
                );
            },
        },
        {
            accessorKey: "status_label",
            header: ({ column }) => (
                <DataTableColumnHeader column={column} title={t('common.status')} />
            ),
            cell: ({ row }) => {
                const assignment = row.original;
                return getStatusBadge(assignment);
            },
        },
        {
            id: "actions",
            header: t('common.actions'),
            cell: ({ row }) => {
                const assignment = row.original;
                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem asChild>
                                <Link href={`/admin/teacher-assignments/${assignment.id}`}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    {t('common.view')}
                                </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                                <Link href={`/admin/teacher-assignments/${assignment.id}/edit`}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    {t('common.edit')}
                                </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                                {t('common.delete')}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    const getStatusBadge = (assignment: TeacherAssignment) => {
        const statusColors = {
            'Active': 'bg-green-500',
            'Inactive': 'bg-gray-500',
            'Scheduled': 'bg-blue-500',
            'Ended': 'bg-red-500',
        };

        const color = statusColors[assignment.status_label as keyof typeof statusColors] || 'bg-gray-500';

        return (
            <Badge variant="default" className={color}>
                {assignment.status_label === 'Active' && <CheckCircle className="w-3 h-3 mr-1" />}
                {assignment.status_label === 'Inactive' && <XCircle className="w-3 h-3 mr-1" />}
                {assignment.status_label}
            </Badge>
        );
    };

    const getCategoryBadge = (category: string) => {
        const variants = {
            core: 'default',
            elective: 'secondary',
            extracurricular: 'outline',
        } as const;

        return <Badge variant={variants[category as keyof typeof variants] || 'outline'}>{category}</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={t('teacher_assignments')} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{t('teacher_assignments')}</h1>
                        <p className="text-muted-foreground">
                            {t('manage_teacher_assignments_description', { school: school.localized_name })}
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/admin/teacher-assignments/create">
                            <Plus className="mr-2 h-4 w-4" />
                            {t('common.add')} {t('common.assignment')}
                        </Link>
                    </Button>
                </div>

                {/* Data Table */}
                <DataTable
                    columns={columns}
                    data={assignments}
                    searchKey="teacher.name"
                    searchPlaceholder={t('search_teacher_assignments')}
                    enableRowSelection={false}
                    enableColumnVisibility={true}
                    enableSorting={true}
                    enableFiltering={true}
                    enablePagination={true}
                    pageSize={25}
                    pageSizeOptions={[10, 25, 50, 100]}
                    emptyMessage={
                        Object.values(filters).some(Boolean)
                            ? t('no_assignments_match_filters')
                            : t('no_teacher_assignments_found')
                    }
                />
            </div>
        </AppLayout>
    );
}

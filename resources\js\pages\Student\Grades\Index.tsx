import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Award, BarChart3, BookOpen, Calendar, Eye, Filter, TrendingUp, TrendingDown, Target, Users } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface Teacher {
    id: number;
    name: string;
    email: string;
}

interface SchoolClass {
    id: number;
    name: string;
    grade_level: string;
    section?: string;
}

interface Subject {
    id: number;
    name: string;
    localized_name: string;
    category: string;
}

interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
}

interface Grade {
    id: number;
    teacher: Teacher;
    school_class: SchoolClass;
    subject: Subject;
    academic_year: AcademicYear;
    grade_type: string;
    grade_type_label: string;
    title: string;
    description?: string;
    score: number;
    max_score: number;
    percentage: number;
    letter_grade: string;
    gpa_points: number;
    assessment_date: string;
    due_date?: string;
    graded_date?: string;
    weight: number;
    status: 'published';
    student_feedback?: string;
    is_extra_credit: boolean;
    is_makeup: boolean;
}

interface SubjectStats {
    subject: Subject;
    stats: {
        count: number;
        average_percentage: number;
        letter_grade: string;
        gpa_points: number;
        highest: number;
        lowest: number;
    };
}

interface OverallStats {
    total_grades: number;
    subjects_count: number;
    overall_average: number;
    overall_letter_grade: string;
    overall_gpa: number;
    highest_grade: number;
    lowest_grade: number;
    grade_distribution: Record<string, number>;
}

interface IndexProps {
    grades: Grade[];
    academicYears: AcademicYear[];
    subjects: Subject[];
    gradeTypes: Record<string, string>;
    filters: {
        academic_year_id?: string;
        subject_id?: string;
        grade_type?: string;
    };
    subjectStats: SubjectStats[];
    overallStats: OverallStats | null;
    message?: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Student', href: '#' },
    { title: 'My Grades', href: '/student/grades' },
];

export default function Index({ grades, academicYears, subjects, gradeTypes, filters, subjectStats, overallStats, message }: IndexProps) {
    const { t } = useTranslation();

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filters };
        if (value === 'all') {
            delete newFilters[key as keyof typeof filters];
        } else {
            newFilters[key as keyof typeof filters] = value;
        }
        
        router.get('/student/grades', newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getGradeTypeBadge = (grade: Grade) => {
        const typeColors = {
            'Assignment': 'bg-blue-100 text-blue-800 border-blue-200',
            'Quiz': 'bg-purple-100 text-purple-800 border-purple-200',
            'Exam': 'bg-red-100 text-red-800 border-red-200',
            'Project': 'bg-green-100 text-green-800 border-green-200',
            'Participation': 'bg-orange-100 text-orange-800 border-orange-200',
            'Homework': 'bg-indigo-100 text-indigo-800 border-indigo-200',
            'Lab Work': 'bg-teal-100 text-teal-800 border-teal-200',
            'Presentation': 'bg-pink-100 text-pink-800 border-pink-200',
        };
        
        const colorClass = typeColors[grade.grade_type_label as keyof typeof typeColors] || 'bg-gray-100 text-gray-800 border-gray-200';
        
        return (
            <Badge variant="outline" className={colorClass}>
                {grade.grade_type_label}
            </Badge>
        );
    };

    const getLetterGradeBadge = (grade: Grade) => {
        const gradeColors = {
            'A+': 'bg-green-500', 'A': 'bg-green-500', 'A-': 'bg-green-400',
            'B+': 'bg-blue-500', 'B': 'bg-blue-500', 'B-': 'bg-blue-400',
            'C+': 'bg-yellow-500', 'C': 'bg-yellow-500', 'C-': 'bg-yellow-400',
            'D+': 'bg-orange-500', 'D': 'bg-orange-500', 'D-': 'bg-orange-400',
            'F': 'bg-red-500',
        };
        
        const color = gradeColors[grade.letter_grade as keyof typeof gradeColors] || 'bg-gray-500';
        
        return (
            <Badge variant="default" className={color}>
                {grade.letter_grade}
            </Badge>
        );
    };

    if (message) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="My Grades" />
                
                <div className="flex h-full flex-1 flex-col gap-6 p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">My Grades</h1>
                            <p className="text-muted-foreground">View your academic performance</p>
                        </div>
                    </div>

                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Award className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Enrollments Found</h3>
                            <p className="text-muted-foreground text-center mb-4">{message}</p>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="My Grades" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">My Grades</h1>
                        <p className="text-muted-foreground">
                            View your academic performance and progress
                        </p>
                    </div>
                </div>

                {/* Overall Statistics */}
                {overallStats && (
                    <div className="grid gap-4 md:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Overall Average</CardTitle>
                                <Target className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{overallStats.overall_average}%</div>
                                <p className="text-xs text-muted-foreground">
                                    {overallStats.overall_letter_grade} ({overallStats.overall_gpa} GPA)
                                </p>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Grades</CardTitle>
                                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{overallStats.total_grades}</div>
                                <p className="text-xs text-muted-foreground">
                                    Across {overallStats.subjects_count} subjects
                                </p>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Highest Grade</CardTitle>
                                <TrendingUp className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">{overallStats.highest_grade}%</div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Lowest Grade</CardTitle>
                                <TrendingDown className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-red-600">{overallStats.lowest_grade}%</div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Subject Statistics */}
                {subjectStats.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Subject Performance</CardTitle>
                            <CardDescription>Your average performance by subject</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {subjectStats.map((subjectStat) => (
                                    <div key={subjectStat.subject.id} className="border rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <h4 className="font-medium">{subjectStat.subject.localized_name}</h4>
                                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                                {subjectStat.stats.letter_grade}
                                            </Badge>
                                        </div>
                                        <div className="text-2xl font-bold mb-1">{subjectStat.stats.average_percentage}%</div>
                                        <div className="text-sm text-muted-foreground">
                                            {subjectStat.stats.count} grades • GPA: {subjectStat.stats.gpa_points}
                                        </div>
                                        <div className="text-xs text-muted-foreground mt-1">
                                            Range: {subjectStat.stats.lowest}% - {subjectStat.stats.highest}%
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Filter className="mr-2 h-4 w-4" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div>
                                <label className="text-sm font-medium mb-2 block">Academic Year</label>
                                <Select
                                    value={filters.academic_year_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('academic_year_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Academic Years" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Academic Years</SelectItem>
                                        {academicYears.map((year) => (
                                            <SelectItem key={year.id} value={year.id.toString()}>
                                                {year.localized_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Subject</label>
                                <Select
                                    value={filters.subject_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('subject_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Subjects" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Subjects</SelectItem>
                                        {subjects.map((subject) => (
                                            <SelectItem key={subject.id} value={subject.id.toString()}>
                                                {subject.localized_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Grade Type</label>
                                <Select
                                    value={filters.grade_type || 'all'}
                                    onValueChange={(value) => handleFilterChange('grade_type', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Types" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Types</SelectItem>
                                        {Object.entries(gradeTypes).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Grades Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {grades.map((grade) => (
                        <Card key={grade.id} className="hover:shadow-md transition-shadow">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg flex items-center">
                                        <BookOpen className="mr-2 h-4 w-4" />
                                        {grade.title}
                                        {grade.is_extra_credit && <Award className="ml-2 h-4 w-4 text-yellow-500" />}
                                    </CardTitle>
                                    {getLetterGradeBadge(grade)}
                                </div>
                                <CardDescription>
                                    <div className="space-y-1">
                                        <div className="flex items-center">
                                            <BookOpen className="mr-1 h-3 w-3" />
                                            {grade.subject.localized_name}
                                        </div>
                                        <div className="flex items-center">
                                            <Users className="mr-1 h-3 w-3" />
                                            {grade.teacher.name} - {grade.school_class.name}
                                        </div>
                                    </div>
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        {getGradeTypeBadge(grade)}
                                        {grade.is_makeup && (
                                            <Badge variant="outline" className="text-blue-600 border-blue-600">
                                                Makeup
                                            </Badge>
                                        )}
                                    </div>
                                    
                                    <div className="text-center">
                                        <div className="text-2xl font-bold">
                                            {grade.score}/{grade.max_score}
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                            {grade.percentage}% (Weight: {grade.weight})
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Calendar className="mr-2 h-4 w-4" />
                                        {new Date(grade.assessment_date).toLocaleDateString()}
                                        {grade.graded_date && (
                                            <span className="ml-2">
                                                (Graded: {new Date(grade.graded_date).toLocaleDateString()})
                                            </span>
                                        )}
                                    </div>
                                    
                                    {grade.student_feedback && (
                                        <div className="text-sm bg-blue-50 p-2 rounded border-l-4 border-blue-200">
                                            <strong>Feedback:</strong> {grade.student_feedback}
                                        </div>
                                    )}

                                    <div className="flex gap-2 pt-2">
                                        <Button variant="outline" size="sm" asChild className="flex-1">
                                            <Link href={`/student/grades/${grade.id}`}>
                                                <Eye className="mr-1 h-3 w-3" />
                                                View Details
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {grades.length === 0 && !message && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Award className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Grades Found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                {Object.values(filters).some(Boolean)
                                    ? 'No grades match your current filters.' 
                                    : 'You don\'t have any published grades yet.'
                                }
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}

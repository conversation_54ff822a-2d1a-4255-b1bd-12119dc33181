import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Plus, BarChart3, Users, BookOpen, Calendar, Eye, Edit, Filter, FileText, Award, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface Student {
    id: number;
    name: string;
    email: string;
}

interface Teacher {
    id: number;
    name: string;
    email: string;
}

interface SchoolClass {
    id: number;
    name: string;
    grade_level: string;
    section?: string;
}

interface Subject {
    id: number;
    name: string;
    localized_name: string;
    category: string;
}

interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
}

interface Grade {
    id: number;
    student: Student;
    teacher: Teacher;
    school_class: SchoolClass;
    subject: Subject;
    academic_year: AcademicYear;
    grade_type: string;
    grade_type_label: string;
    title: string;
    description?: string;
    score: number;
    max_score: number;
    percentage: number;
    letter_grade: string;
    gpa_points: number;
    assessment_date: string;
    due_date?: string;
    graded_date?: string;
    weight: number;
    status: 'draft' | 'published' | 'archived';
    status_label: string;
    teacher_notes?: string;
    student_feedback?: string;
    is_extra_credit: boolean;
    is_makeup: boolean;
    is_published: boolean;
    is_overdue: boolean;
}

interface TeacherAssignment {
    id: number;
    school_class: SchoolClass;
    subject: Subject;
    academic_year: AcademicYear;
    is_primary: boolean;
}

interface IndexProps {
    grades: Grade[];
    academicYears: AcademicYear[];
    classes: SchoolClass[];
    subjects: Subject[];
    gradeTypes: Record<string, string>;
    statuses: Record<string, string>;
    filters: {
        academic_year_id?: string;
        class_id?: string;
        subject_id?: string;
        grade_type?: string;
        status?: string;
    };
    teacherAssignments: TeacherAssignment[];
    message?: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Teaching', href: '#' },
    { title: 'Grades', href: '/teacher/grades' },
];

export default function Index({ grades, academicYears, classes, subjects, gradeTypes, statuses, filters, teacherAssignments, message }: IndexProps) {
    const { t } = useTranslation();

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filters };
        if (value === 'all') {
            delete newFilters[key as keyof typeof filters];
        } else {
            newFilters[key as keyof typeof filters] = value;
        }
        
        router.get('/teacher/grades', newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusBadge = (grade: Grade) => {
        const statusColors = {
            'Draft': 'bg-yellow-500',
            'Published': 'bg-green-500',
            'Archived': 'bg-gray-500',
        };
        
        const color = statusColors[grade.status_label as keyof typeof statusColors] || 'bg-gray-500';
        
        return (
            <Badge variant="default" className={color}>
                {grade.status_label === 'Draft' && <Clock className="w-3 h-3 mr-1" />}
                {grade.status_label === 'Published' && <CheckCircle className="w-3 h-3 mr-1" />}
                {grade.status_label === 'Archived' && <FileText className="w-3 h-3 mr-1" />}
                {grade.status_label}
            </Badge>
        );
    };

    const getGradeTypeBadge = (grade: Grade) => {
        const typeColors = {
            'Assignment': 'bg-blue-100 text-blue-800 border-blue-200',
            'Quiz': 'bg-purple-100 text-purple-800 border-purple-200',
            'Exam': 'bg-red-100 text-red-800 border-red-200',
            'Project': 'bg-green-100 text-green-800 border-green-200',
            'Participation': 'bg-orange-100 text-orange-800 border-orange-200',
            'Homework': 'bg-indigo-100 text-indigo-800 border-indigo-200',
            'Lab Work': 'bg-teal-100 text-teal-800 border-teal-200',
            'Presentation': 'bg-pink-100 text-pink-800 border-pink-200',
        };
        
        const colorClass = typeColors[grade.grade_type_label as keyof typeof typeColors] || 'bg-gray-100 text-gray-800 border-gray-200';
        
        return (
            <Badge variant="outline" className={colorClass}>
                {grade.grade_type_label}
            </Badge>
        );
    };

    const getLetterGradeBadge = (grade: Grade) => {
        const gradeColors = {
            'A+': 'bg-green-500', 'A': 'bg-green-500', 'A-': 'bg-green-400',
            'B+': 'bg-blue-500', 'B': 'bg-blue-500', 'B-': 'bg-blue-400',
            'C+': 'bg-yellow-500', 'C': 'bg-yellow-500', 'C-': 'bg-yellow-400',
            'D+': 'bg-orange-500', 'D': 'bg-orange-500', 'D-': 'bg-orange-400',
            'F': 'bg-red-500',
        };
        
        const color = gradeColors[grade.letter_grade as keyof typeof gradeColors] || 'bg-gray-500';
        
        return (
            <Badge variant="default" className={color}>
                {grade.letter_grade}
            </Badge>
        );
    };

    if (message) {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="Grades" />
                
                <div className="flex h-full flex-1 flex-col gap-6 p-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Grades</h1>
                            <p className="text-muted-foreground">Manage student grades and assessments</p>
                        </div>
                    </div>

                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Teaching Assignments</h3>
                            <p className="text-muted-foreground text-center mb-4">{message}</p>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Grades" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Grades</h1>
                        <p className="text-muted-foreground">
                            Manage student grades and assessments for your classes
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/teacher/grades/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Grade
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <Filter className="mr-2 h-4 w-4" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-5">
                            <div>
                                <label className="text-sm font-medium mb-2 block">Academic Year</label>
                                <Select
                                    value={filters.academic_year_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('academic_year_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Academic Years" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Academic Years</SelectItem>
                                        {academicYears.map((year) => (
                                            <SelectItem key={year.id} value={year.id.toString()}>
                                                {year.localized_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Class</label>
                                <Select
                                    value={filters.class_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('class_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Classes" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Classes</SelectItem>
                                        {classes.map((schoolClass) => (
                                            <SelectItem key={schoolClass.id} value={schoolClass.id.toString()}>
                                                {schoolClass.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Subject</label>
                                <Select
                                    value={filters.subject_id || 'all'}
                                    onValueChange={(value) => handleFilterChange('subject_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Subjects" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Subjects</SelectItem>
                                        {subjects.map((subject) => (
                                            <SelectItem key={subject.id} value={subject.id.toString()}>
                                                {subject.localized_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Grade Type</label>
                                <Select
                                    value={filters.grade_type || 'all'}
                                    onValueChange={(value) => handleFilterChange('grade_type', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Types" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Types</SelectItem>
                                        {Object.entries(gradeTypes).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <label className="text-sm font-medium mb-2 block">Status</label>
                                <Select
                                    value={filters.status || 'all'}
                                    onValueChange={(value) => handleFilterChange('status', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Statuses" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Statuses</SelectItem>
                                        {Object.entries(statuses).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>
                                                {label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Grades Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {grades.map((grade) => (
                        <Card key={grade.id} className="hover:shadow-md transition-shadow">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-lg flex items-center">
                                        <BookOpen className="mr-2 h-4 w-4" />
                                        {grade.title}
                                        {grade.is_extra_credit && <Award className="ml-2 h-4 w-4 text-yellow-500" />}
                                        {grade.is_makeup && <AlertCircle className="ml-2 h-4 w-4 text-blue-500" />}
                                    </CardTitle>
                                    {getStatusBadge(grade)}
                                </div>
                                <CardDescription>
                                    <div className="space-y-1">
                                        <div className="flex items-center">
                                            <Users className="mr-1 h-3 w-3" />
                                            {grade.student.name} - {grade.school_class.name}
                                        </div>
                                        <div className="flex items-center">
                                            <BookOpen className="mr-1 h-3 w-3" />
                                            {grade.subject.localized_name}
                                        </div>
                                    </div>
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        {getGradeTypeBadge(grade)}
                                        {getLetterGradeBadge(grade)}
                                    </div>
                                    
                                    <div className="text-center">
                                        <div className="text-2xl font-bold">
                                            {grade.score}/{grade.max_score}
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                            {grade.percentage}% (Weight: {grade.weight})
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Calendar className="mr-2 h-4 w-4" />
                                        {new Date(grade.assessment_date).toLocaleDateString()}
                                        {grade.graded_date && (
                                            <span className="ml-2">
                                                (Graded: {new Date(grade.graded_date).toLocaleDateString()})
                                            </span>
                                        )}
                                    </div>
                                    
                                    {grade.description && (
                                        <div className="text-sm text-muted-foreground">
                                            <p className="truncate">{grade.description}</p>
                                        </div>
                                    )}

                                    <div className="flex gap-2 pt-2">
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/teacher/grades/${grade.id}`}>
                                                <Eye className="mr-1 h-3 w-3" />
                                                View
                                            </Link>
                                        </Button>
                                        <Button variant="outline" size="sm" asChild>
                                            <Link href={`/teacher/grades/${grade.id}/edit`}>
                                                <Edit className="mr-1 h-3 w-3" />
                                                Edit
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {grades.length === 0 && !message && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Grades Found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                {Object.values(filters).some(Boolean)
                                    ? 'No grades match your current filters.' 
                                    : 'Get started by creating your first grade entry.'
                                }
                            </p>
                            <Button asChild>
                                <Link href="/teacher/grades/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Grade
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}

import { DashboardStats } from '@/components/dashboard/dashboard-stats';
import { QuickActions } from '@/components/dashboard/quick-actions';
import { RecentActivities } from '@/components/dashboard/recent-activities';
import { RoleWelcome } from '@/components/dashboard/role-welcome';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type DashboardData, type User } from '@/types';
import { Head } from '@inertiajs/react';

interface DashboardProps {
    user: User;
    dashboardData: DashboardData;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard({ user, dashboardData }: DashboardProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <RoleWelcome user={user} />

                <DashboardStats stats={dashboardData.stats} role={dashboardData.role} />

                <div className="grid gap-6 md:grid-cols-2">
                    <QuickActions actions={dashboardData.quick_actions} />
                    <RecentActivities activities={dashboardData.recent_activities} />
                </div>
            </div>
        </AppLayout>
    );
}

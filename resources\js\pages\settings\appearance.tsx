import { Head } from '@inertiajs/react';

import AppearanceTabs from '@/components/appearance-tabs';
import HeadingSmall from '@/components/heading-small';
import ThemePresetSelect from '@/components/theme-preset-select';
import { type BreadcrumbItem } from '@/types';

import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';
import { useThemePresetStore } from '@/store/theme-preset-store';
import { useEditorStore } from '@/store/editor-store';
import { useEffect } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Appearance settings',
        href: '/settings/appearance',
    },
];

export default function Appearance() {
    const { presets, loadSavedPresets } = useThemePresetStore();
    const { themeState, applyThemePreset } = useEditorStore();

    useEffect(() => {
        // Load any saved presets when the component mounts
        loadSavedPresets();
    }, [loadSavedPresets]);

    // Set a default theme preset if none is selected
    useEffect(() => {
        if (!themeState.preset) {
            console.log("No theme preset selected, applying default");
            applyThemePreset("modern-minimal");
        } else {
            console.log("Current theme preset:", themeState.preset);
        }
    }, [themeState.preset, applyThemePreset]);

    const handleThemePresetChange = (presetName: string) => {
        console.log("Changing theme preset to:", presetName);
        applyThemePreset(presetName);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Appearance settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall title="Appearance settings" description="Update your account's appearance settings" />
                    <AppearanceTabs />
                    <div className="mt-8">
                        <h2 className="text-xl font-semibold mb-4">Theme Settings</h2>
                        <div className="bg-card rounded-lg border border-border">
                            <ThemePresetSelect
                                presets={presets}
                                currentPreset={themeState.preset || null}
                                onPresetChange={handleThemePresetChange}
                            />
                        </div>
                    </div>
                </div>
            </SettingsLayout>
        </AppLayout>
    );
}

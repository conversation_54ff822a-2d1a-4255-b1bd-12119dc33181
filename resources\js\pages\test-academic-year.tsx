import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { TestAcademicYearStore } from '@/test-academic-year-store';
import { AcademicYearSelector } from '@/components/academic-year-selector';
import { useAcademicYearStore } from '@/stores/academic-year-store';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestAcademicYear() {
    const { academicYears, selectedAcademicYear, isLoading } = useAcademicYearStore();

    return (
        <AppLayout>
            <Head title="Test Academic Year System" />
            
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Academic Year System Test</h1>
                    <p className="text-muted-foreground">
                        Test page to verify the academic year store and selector functionality.
                    </p>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Academic Year Selector</CardTitle>
                            <CardDescription>
                                Test the academic year selector component
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <AcademicYearSelector />
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Store Status</CardTitle>
                            <CardDescription>
                                Current state of the academic year store
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2 text-sm">
                                <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
                                <p><strong>Academic Years:</strong> {academicYears.length}</p>
                                <p><strong>Selected:</strong> {selectedAcademicYear?.name || 'None'}</p>
                                <p><strong>Current:</strong> {academicYears.find(ay => ay.is_current)?.name || 'None'}</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <TestAcademicYearStore />
            </div>
        </AppLayout>
    );
}

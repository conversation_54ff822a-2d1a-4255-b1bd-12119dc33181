import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ThemeEditorState } from "../types/theme";
import { defaultThemeState } from "../config/theme";
import { getPresetThemeStyles } from "../utils/theme-preset-helper";
import { isDeepEqual } from "../utils/deep-equal";

interface EditorStore {
  themeState: ThemeEditorState;
  themeCheckpoint: ThemeEditorState | null;
  setThemeState: (state: ThemeEditorState) => void;
  applyThemePreset: (preset: string) => void;
  hasUnsavedChanges: () => boolean;
}

export const useEditorStore = create<EditorStore>()(
  persist(
    (set, get) => ({
      themeState: defaultThemeState,
      themeCheckpoint: null,
      setThemeState: (state: ThemeEditorState) => {
        set({ themeState: state });
      },
      applyThemePreset: (preset: string) => {
        console.log("Applying theme preset:", preset);
        const themeState = get().themeState;
        const newStyles = getPresetThemeStyles(preset);
        console.log("New styles:", newStyles);
        const newThemeState: ThemeEditorState = {
          ...themeState,
          preset,
          styles: newStyles,
        };
        set({
          themeState: newThemeState,
          themeCheckpoint: newThemeState,
        });

        // Force a re-render by applying the theme to the document element
        const root = document.documentElement;
        if (root) {
          const currentMode = newThemeState.currentMode;
          root.setAttribute("data-theme", currentMode);

          // Apply all theme properties as CSS variables
          Object.entries(newStyles[currentMode]).forEach(([key, value]) => {
            root.style.setProperty(`--${key}`, value as string);
          });
        }
      },
      hasUnsavedChanges: () => {
        const themeState = get().themeState;
        if (!themeState.preset) return false;

        const presetThemeStyles = getPresetThemeStyles(themeState.preset);
        return !isDeepEqual(themeState.styles, presetThemeStyles);
      },
    }),
    {
      name: "editor-storage",
    }
  )
);

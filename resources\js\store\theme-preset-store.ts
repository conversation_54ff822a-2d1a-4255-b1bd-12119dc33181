import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ThemePreset, ThemeStyles } from "../types/theme";
import { defaultPresets } from "../utils/theme-presets";

interface ThemePresetStore {
  presets: Record<string, ThemePreset>;
  registerPreset: (name: string, preset: ThemePreset) => void;
  unregisterPreset: (name: string) => void;
  updatePreset: (name: string, preset: ThemePreset) => void;
  getPreset: (name: string) => ThemePreset | undefined;
  getAllPresets: () => Record<string, ThemePreset>;
  loadSavedPresets: () => Promise<void>;
}

export const useThemePresetStore = create<ThemePresetStore>()(
  persist(
    (set, get) => ({
      presets: defaultPresets,
      registerPreset: (name: string, preset: ThemePreset) => {
        set((state) => ({
          presets: {
            ...state.presets,
            [name]: preset,
          },
        }));
      },
      unregisterPreset: (name: string) => {
        set((state) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { [name]: removed, ...remainingPresets } = state.presets;
          return {
            presets: remainingPresets,
          };
        });
      },
      updatePreset: (name: string, preset: ThemePreset) => {
        set((state) => ({
          presets: {
            ...state.presets,
            [name]: preset,
          },
        }));
      },
      getPreset: (name: string) => {
        return get().presets[name];
      },
      getAllPresets: () => {
        return get().presets;
      },
      loadSavedPresets: async () => {
        try {
          // Mock saved themes since the API endpoint doesn't exist yet
          // In a real application, this would be an API call
          // const savedThemes = await fetch('/api/themes').then(res => res.json());

          // Mock data for demonstration
          const savedThemes = [
            {
              id: "saved-amethyst",
              name: "My Amethyst Theme",
              styles: defaultPresets["amethyst-haze"].styles,
              createdAt: new Date().toISOString(),
            },
            {
              id: "saved-bubblegum",
              name: "My Bubblegum Theme",
              styles: defaultPresets["bubblegum"].styles,
              createdAt: new Date().toISOString(),
            }
          ];

          interface SavedTheme {
            id: string;
            name: string;
            styles: ThemeStyles;
            createdAt: string;
          }

          const savedPresets = savedThemes.reduce((acc: Record<string, ThemePreset>, theme: SavedTheme) => {
            acc[theme.id] = {
              label: theme.name,
              styles: theme.styles,
              source: "SAVED",
              createdAt: theme.createdAt,
            };
            return acc;
          }, {});

          set((state) => ({
            presets: {
              ...state.presets,
              ...savedPresets,
            },
          }));
        } catch (error) {
          console.error("Failed to load saved presets:", error);

          // Fallback to some default saved presets if the API call fails
          const fallbackSavedPresets: Record<string, ThemePreset> = {
            "saved-default": {
              label: "My Default Theme",
              styles: defaultPresets["modern-minimal"].styles,
              source: "SAVED",
              createdAt: new Date().toISOString(),
            }
          };

          set((state) => ({
            presets: {
              ...state.presets,
              ...fallbackSavedPresets,
            },
          }));
        }
      },
    }),
    {
      name: "theme-presets-storage",
    }
  )
);

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
    start_date: string;
    end_date: string;
    is_current: boolean;
    is_active: boolean;
}

interface AcademicYearState {
    // State
    selectedAcademicYear: AcademicYear | null;
    availableAcademicYears: AcademicYear[];
    isLoading: boolean;
    error: string | null;

    // Actions
    setSelectedAcademicYear: (academicYear: AcademicYear | null) => void;
    setAvailableAcademicYears: (academicYears: AcademicYear[]) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    initializeFromProps: (academicYears: AcademicYear[], currentAcademicYear?: AcademicYear | null) => void;
    reset: () => void;

    // Getters
    getCurrentAcademicYearId: () => number | null;
    isCurrentAcademicYear: (academicYearId: number) => boolean;

    // Computed properties
    academicYears: AcademicYear[]; // Alias for availableAcademicYears for consistency
}

const initialState = {
    selectedAcademicYear: null,
    availableAcademicYears: [],
    isLoading: false,
    error: null,
};

export const useAcademicYearStore = create<AcademicYearState>()(
    persist(
        (set, get) => ({
            ...initialState,

            setSelectedAcademicYear: (academicYear) => {
                set({ selectedAcademicYear: academicYear, error: null });
            },

            setAvailableAcademicYears: (academicYears) => {
                set({ availableAcademicYears: academicYears });
            },

            setLoading: (loading) => {
                set({ isLoading: loading });
            },

            setError: (error) => {
                set({ error, isLoading: false });
            },

            initializeFromProps: (academicYears, currentAcademicYear) => {
                const state = get();

                // Set available academic years
                set({ availableAcademicYears: academicYears });

                // If no academic year is selected, try to set a default
                if (!state.selectedAcademicYear) {
                    let defaultAcademicYear = null;

                    // Priority 1: Use provided current academic year
                    if (currentAcademicYear) {
                        defaultAcademicYear = currentAcademicYear;
                    }
                    // Priority 2: Find the current academic year from the list
                    else {
                        defaultAcademicYear = academicYears.find(ay => ay.is_current) || null;
                    }
                    // Priority 3: Use the first active academic year
                    if (!defaultAcademicYear) {
                        defaultAcademicYear = academicYears.find(ay => ay.is_active) || null;
                    }
                    // Priority 4: Use the first academic year
                    if (!defaultAcademicYear && academicYears.length > 0) {
                        defaultAcademicYear = academicYears[0];
                    }

                    if (defaultAcademicYear) {
                        set({ selectedAcademicYear: defaultAcademicYear });
                    }
                }
                // If an academic year is already selected, verify it's still available
                else {
                    const isStillAvailable = academicYears.some(
                        ay => ay.id === state.selectedAcademicYear?.id
                    );

                    if (!isStillAvailable) {
                        // Reset to default if the selected academic year is no longer available
                        const defaultAcademicYear = currentAcademicYear ||
                            academicYears.find(ay => ay.is_current) ||
                            academicYears.find(ay => ay.is_active) ||
                            academicYears[0] || null;

                        set({ selectedAcademicYear: defaultAcademicYear });
                    }
                }
            },

            reset: () => {
                set(initialState);
            },

            getCurrentAcademicYearId: () => {
                return get().selectedAcademicYear?.id || null;
            },

            isCurrentAcademicYear: (academicYearId) => {
                const currentId = get().getCurrentAcademicYearId();
                return currentId === academicYearId;
            },

            // Computed property for consistency
            get academicYears() {
                return get().availableAcademicYears;
            },
        }),
        {
            name: 'academic-year-storage',
            partialize: (state) => ({
                selectedAcademicYear: state.selectedAcademicYear,
            }),
        }
    )
);

// Hook for easy access to academic year ID in components
export const useCurrentAcademicYearId = () => {
    return useAcademicYearStore(state => state.getCurrentAcademicYearId());
};

// Hook for checking if a specific academic year is current
export const useIsCurrentAcademicYear = () => {
    return useAcademicYearStore(state => state.isCurrentAcademicYear);
};

// Hook for getting selected academic year
export const useSelectedAcademicYear = () => {
    return useAcademicYearStore(state => state.selectedAcademicYear);
};

// Hook for getting available academic years
export const useAvailableAcademicYears = () => {
    return useAcademicYearStore(state => state.availableAcademicYears);
};

import React from 'react';
import { useAcademicYearStore } from './stores/academic-year-store';
import { AcademicYearSelector } from './components/academic-year-selector';

/**
 * Test component to verify the Academic Year Store functionality
 * This can be temporarily added to a page to test the store
 */
export function TestAcademicYearStore() {
    const {
        academicYears,
        currentAcademicYear,
        selectedAcademicYear,
        isLoading,
        error,
        setSelectedAcademicYear,
        setCurrentAcademicYear,
        initializeFromProps
    } = useAcademicYearStore();

    // Test data
    const testAcademicYears = [
        {
            id: 1,
            name: '2023-2024',
            localized_name: '2023-2024',
            start_date: '2023-09-01',
            end_date: '2024-06-30',
            is_current: false,
            is_active: true,
        },
        {
            id: 2,
            name: '2024-2025',
            localized_name: '2024-2025',
            start_date: '2024-09-01',
            end_date: '2025-06-30',
            is_current: true,
            is_active: true,
        },
    ];

    const handleInitialize = () => {
        initializeFromProps(testAcademicYears, testAcademicYears[1]);
    };

    const handleSetSelected = (academicYear: typeof testAcademicYears[0]) => {
        setSelectedAcademicYear(academicYear);
    };

    return (
        <div className="p-6 space-y-6 bg-white rounded-lg shadow">
            <h2 className="text-2xl font-bold">Academic Year Store Test</h2>
            
            <div className="space-y-4">
                <div>
                    <h3 className="text-lg font-semibold">Store State:</h3>
                    <div className="text-sm space-y-1">
                        <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
                        <p><strong>Error:</strong> {error || 'None'}</p>
                        <p><strong>Academic Years Count:</strong> {academicYears.length}</p>
                        <p><strong>Current Academic Year:</strong> {currentAcademicYear?.name || 'None'}</p>
                        <p><strong>Selected Academic Year:</strong> {selectedAcademicYear?.name || 'None'}</p>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold">Actions:</h3>
                    <div className="space-x-2">
                        <button
                            onClick={handleInitialize}
                            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                        >
                            Initialize Store
                        </button>
                        <button
                            onClick={() => handleSetSelected(testAcademicYears[0])}
                            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                        >
                            Select 2023-2024
                        </button>
                        <button
                            onClick={() => handleSetSelected(testAcademicYears[1])}
                            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                        >
                            Select 2024-2025
                        </button>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold">Academic Year Selector Component:</h3>
                    <AcademicYearSelector />
                </div>

                <div>
                    <h3 className="text-lg font-semibold">Academic Years List:</h3>
                    <ul className="space-y-2">
                        {academicYears.map((year) => (
                            <li key={year.id} className="p-2 border rounded">
                                <div className="flex justify-between items-center">
                                    <span>{year.localized_name}</span>
                                    <div className="space-x-2">
                                        {year.is_current && (
                                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                                Current
                                            </span>
                                        )}
                                        {year.is_active && (
                                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                                                Active
                                            </span>
                                        )}
                                        {selectedAcademicYear?.id === year.id && (
                                            <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                                                Selected
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
        </div>
    );
}

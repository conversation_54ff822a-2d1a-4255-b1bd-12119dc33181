import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface AcademicYear {
    id: number;
    name: string;
    localized_name: string;
    start_date: string;
    end_date: string;
    is_current: boolean;
    is_active: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    locale: string;
    translations: Record<string, any>;
    academicYears: AcademicYear[];
    currentAcademicYear: AcademicYear | null;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    role: 'admin' | 'teacher' | 'student' | 'parent';
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}

export interface QuickAction {
    title: string;
    href: string;
    icon: string;
}

export interface DashboardData {
    role: string;
    stats: Record<string, number>;
    recent_activities: string[];
    quick_actions: QuickAction[];
}

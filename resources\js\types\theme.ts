export interface ThemeStyleProps {
  // Base colors
  background: string;
  foreground: string;

  // Component colors
  primary: string;
  "primary-foreground": string;
  secondary: string;
  "secondary-foreground": string;
  accent: string;
  "accent-foreground": string;
  muted: string;
  "muted-foreground": string;

  // UI element colors
  card: string;
  "card-foreground": string;
  popover: string;
  "popover-foreground": string;
  border: string;
  input: string;
  ring: string;

  // Utility colors
  destructive: string;
  "destructive-foreground": string;

  // Chart colors
  "chart-1"?: string;
  "chart-2"?: string;
  "chart-3"?: string;
  "chart-4"?: string;
  "chart-5"?: string;

  // Sidebar colors
  sidebar?: string;
  "sidebar-foreground"?: string;
  "sidebar-primary"?: string;
  "sidebar-primary-foreground"?: string;
  "sidebar-accent"?: string;
  "sidebar-accent-foreground"?: string;
  "sidebar-border"?: string;
  "sidebar-ring"?: string;

  // Shadow properties
  "shadow-color"?: string;
  "shadow-opacity"?: string;
  "shadow-blur"?: string;
  "shadow-spread"?: string;
  "shadow-offset-x"?: string;
  "shadow-offset-y"?: string;

  // Typography
  "font-sans": string;
  "font-serif": string;
  "font-mono": string;

  // Other properties
  radius: string;
  spacing?: string;
  "letter-spacing"?: string;
}

export interface ThemeStyles {
  light: ThemeStyleProps;
  dark: ThemeStyleProps;
}

export interface ThemePreset {
  label: string;
  source?: "SAVED" | "BUILT_IN";
  createdAt?: string;
  styles: ThemeStyles;
}

export interface ThemeEditorState {
  styles: ThemeStyles;
  currentMode: "light" | "dark";
  preset?: string;
}

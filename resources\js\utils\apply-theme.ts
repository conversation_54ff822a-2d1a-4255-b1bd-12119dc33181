import { ThemeEditorState, ThemeStyleProps } from "../types/theme";
import { COMMON_STYLES } from "../config/theme";

export function applyThemeToElement(
  themeState: ThemeEditorState,
  element: HTMLElement
) {
  const { styles, currentMode } = themeState;
  const currentTheme = styles[currentMode];

  console.log("Applying theme to element:", currentMode, themeState.preset);
  console.log("Current theme styles:", currentTheme);

  // Apply the current theme mode as a data attribute
  element.setAttribute("data-theme", currentMode);

  // Apply all theme properties as CSS variables
  Object.entries(currentTheme).forEach(([key, value]) => {
    if (value) {
      console.log(`Setting CSS variable: --${key} = ${value}`);
      element.style.setProperty(`--${key}`, value);
    }
  });

  // Apply common styles from the other theme mode
  const otherMode = currentMode === "light" ? "dark" : "light";
  const otherTheme = styles[otherMode];

  COMMON_STYLES.forEach((key) => {
    if (key in otherTheme) {
      const value = otherTheme[key as keyof ThemeStyleProps];
      if (value) {
        console.log(`Setting common CSS variable: --${key} = ${value}`);
        element.style.setProperty(`--${key}`, value);
      }
    }
  });

  // Force a repaint to ensure the theme is applied
  document.body.style.display = 'none';
  document.body.offsetHeight; // Trigger a reflow
  document.body.style.display = '';
}

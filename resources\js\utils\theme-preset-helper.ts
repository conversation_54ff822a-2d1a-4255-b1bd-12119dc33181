import { defaultThemeState } from "../config/theme";
import { ThemeStyles } from "../types/theme";
import { useThemePresetStore } from "../store/theme-preset-store";
import { defaultPresets } from "./theme-presets";

export function getPresetThemeStyles(name: string): ThemeStyles {
  console.log("Getting preset theme styles for:", name);
  const defaultTheme = defaultThemeState.styles;

  if (name === "default") {
    return defaultTheme;
  }

  // Check if it's a built-in preset
  if (defaultPresets[name]) {
    console.log("Found built-in preset:", name);
    return defaultPresets[name].styles;
  }

  // Check if it's a saved preset
  const store = useThemePresetStore.getState();
  const preset = store.getPreset(name);

  if (!preset) {
    console.log("Preset not found, using default theme:", name);
    return defaultTheme;
  }

  console.log("Found saved preset:", name);
  return {
    light: {
      ...defaultTheme.light,
      ...(preset.styles.light || {}),
    },
    dark: {
      ...defaultTheme.dark,
      ...(preset.styles.dark || {}),
    },
  };
}

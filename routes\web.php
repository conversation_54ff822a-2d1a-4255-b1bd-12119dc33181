<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Language switching routes (accessible without authentication)
Route::get('language/{locale}', [App\Http\Controllers\LanguageController::class, 'switch'])->name('language.switch');
Route::get('api/languages', [App\Http\Controllers\LanguageController::class, 'getAvailableLanguages'])->name('api.languages');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Admin routes
    Route::prefix('admin')->name('admin.')->middleware('role:admin')->group(function () {
        Route::get('school-settings', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'index'])->name('school-settings');
        Route::post('school-settings', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'store'])->name('school-settings.store');
        Route::delete('school-settings/logo', [App\Http\Controllers\Admin\SchoolSettingsController::class, 'removeLogo'])->name('school-settings.remove-logo');

        // Academic Year Management
        Route::resource('academic-years', App\Http\Controllers\Admin\AcademicYearController::class);
        Route::post('academic-years/{academicYear}/set-current', [App\Http\Controllers\Admin\AcademicYearController::class, 'setCurrent'])->name('academic-years.set-current');

        // Period Management
        Route::resource('periods', App\Http\Controllers\Admin\PeriodController::class);
        Route::post('periods/{period}/set-current', [App\Http\Controllers\Admin\PeriodController::class, 'setCurrent'])->name('periods.set-current');

        // Class Management
        Route::resource('classes', App\Http\Controllers\Admin\ClassController::class);

        // Subject Management
        Route::resource('subjects', App\Http\Controllers\Admin\SubjectController::class);
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

<context>
# Overview  
NewEduco is a comprehensive educational management system designed specifically for Tunisian schools to manage academic, administrative, and operational processes. The system addresses the complex needs of educational institutions by providing integrated modules for academic management, student services, financial tracking, and communication. Built with modern web technologies (Laravel + Inertia + ReactJS + shadcn UI), it offers multilingual support (Arabic RTL/French LTR) and real-time capabilities to streamline school operations and enhance educational outcomes.

# Core Features  
## Academic Management
- **Class and Course Structure**: Create class hierarchies, assign students, allocate classrooms, manage subjects with multilingual support, course scheduling, and subject coefficient management for grading
- **Online Learning Platform**: Virtual course creation, file uploads (PDF, DOC, DOCX with Google Docs viewer), video embedding, homework assignment/submission system, quiz/examination system, virtual classroom integration (Google Meet, Zoom)
- **Academic Planning**: Academic year configuration, term/period planning, class scheduling, year cloning functionality
- **Student Progress Tracking**: Grade management with multiple examination types, multilingual report generation (Arabic/French), class performance reports, ranking systems

## Student Management
- **Enrollment and Records**: Personal details management, academic history tracking, family information, document management
- **Attendance System**: Class attendance tracking, transportation attendance monitoring, absence reporting with statistics and visualization
- **Student Services**: Cafeteria meal planning/tracking/complaints, transportation route management and vehicle tracking

## Financial Management
- **Fee Management**: Service categories configuration, payment schedule creation, payment tracking, student service assignment
- **Billing System**: Fee collection tracking, payment history, revenue forecasting, comprehensive financial reporting

## Communication System
- **Notifications**: Module-specific notifications, real-time updates, SMS capabilities, Firebase push notifications, email notifications
- **Parent Communication**: Parent request management, announcement system, messaging between parents and staff, document sharing

## System Configuration
- **Settings Management**: School information, academic year settings, notification preferences, module activation/deactivation
- **Security & Access Control**: User authentication, role-based access control, activity logging, permission management

# User Experience  
## User Personas
- **School Administrators**: Need comprehensive oversight of all school operations, financial tracking, and system configuration
- **Teachers**: Require easy access to class management, grading, attendance tracking, and parent communication
- **Students**: Need access to online learning materials, homework submissions, grade viewing, and schedule information
- **Parents**: Want to monitor their children's progress, communicate with teachers, and receive notifications

## Key User Flows
- **Student Enrollment**: Registration → Class assignment → Service enrollment → Payment setup
- **Academic Management**: Year setup → Class creation → Subject assignment → Teacher allocation → Schedule generation
- **Grade Management**: Examination setup → Grade entry → Report generation → Parent notification
- **Online Learning**: Course creation → Content upload → Assignment distribution → Submission collection → Grading

## UI/UX Considerations
- Multilingual interface with seamless Arabic (RTL) and French (LTR) switching
- Responsive design for desktop and mobile access
- Intuitive navigation with role-based dashboards
- Real-time updates and notifications
- Accessibility compliance for diverse user needs
</context>
<PRD>
# Technical Architecture  
## System Components
- **Frontend**: React with TypeScript, Tailwind CSS, Shadcn UI components, Zustand for state management, Inertia.js for SPA-like experience
- **Backend**: Laravel 11.x with Inertia.js integration, Laravel Reverb for real-time updates
- **Database**: MySQL/MariaDB with comprehensive schema for users, schools, academic data, attendance, grades, transportation, cafeteria, financial records
- **External Integrations**: SMS Gateway, Firebase push notifications, Google Meet/Zoom for virtual classrooms

## Data Models
- **Core Entities**: Users (admin/teacher/student/parent roles), Schools, Academic Years, Periods/Terms, Classes, Subjects, Students, Teachers
- **Academic Data**: Attendance Records, Grades/Marks, Examinations, Course Materials, Assignments
- **Services**: Transportation (vehicles, routes, journeys), Cafeteria (menus, meals), Financial Records
- **Communication**: Notifications, Messages, Announcements, System Settings

## APIs and Integrations
- RESTful API design with Inertia.js for seamless frontend-backend communication
- SMS Gateway API for notifications
- Firebase API for push notifications
- Video conferencing API integrations (Google Meet, Zoom)
- File storage and management for documents and media

## Infrastructure Requirements
- PHP 8.2+, MySQL/MariaDB, Web server (Apache/Nginx)
- Node.js for frontend build, Composer for PHP dependencies, NPM/Yarn for JavaScript dependencies
- SSL/HTTPS for security, backup systems, monitoring tools

# Development Roadmap  
## Phase 1: Foundation & Authentication (MVP Core)
- Database schema design and migrations
- User authentication and authorization system
- Role-based access control (admin, teacher, student, parent)
- Basic school configuration and settings
- Multilingual support infrastructure (Arabic RTL/French LTR)
- Basic dashboard layouts for each user role

## Phase 2: Academic Management Core
- Academic year and period/term management
- Class creation and management system
- Subject management with multilingual support
- Teacher assignment to classes and subjects
- Basic student enrollment and class assignment
- Simple grade entry and viewing system

## Phase 3: Student Management & Attendance
- Comprehensive student records management
- Attendance tracking system for classes
- Basic reporting for attendance and grades
- Student profile management with family information
- Document management for student records

## Phase 4: Enhanced Academic Features
- Advanced grade management with multiple examination types
- Report generation system (multilingual PDF reports)
- Class performance analytics and ranking systems
- Academic progress tracking across periods
- Grade calculation with subject coefficients

## Phase 5: Online Learning Platform
- Virtual course creation and management
- File upload system with Google Docs viewer integration
- Video embedding capabilities
- Homework assignment and submission system
- Basic quiz/examination system
- Virtual classroom integration (Google Meet, Zoom links)

## Phase 6: Student Services
- Transportation management (routes, vehicles, journeys)
- Transportation attendance tracking
- Cafeteria services (meal planning, tracking, complaints)
- Menu management system
- Service assignment to students

## Phase 7: Financial Management
- Fee structure and service categories configuration
- Payment schedule creation and management
- Payment tracking and history
- Financial reporting and revenue forecasting
- Billing system integration

## Phase 8: Communication & Notifications
- Notification system infrastructure
- SMS notification integration
- Firebase push notifications
- Email notification system
- Parent-teacher communication platform
- Announcement system
- Real-time updates with Laravel Reverb

## Phase 9: Advanced Features & Optimization
- Advanced quiz and examination system
- Comprehensive analytics and reporting
- System performance optimization
- Advanced security features
- Mobile app considerations
- Data export/import capabilities

## Phase 10: Integration & Polish
- External service integrations refinement
- System-wide testing and quality assurance
- Performance optimization and caching
- Security audits and compliance
- Documentation and training materials
- Deployment and maintenance procedures

# Logical Dependency Chain
## Foundation First (Phases 1-2)
- Authentication and authorization must be established before any other features
- Database schema and basic school configuration are prerequisites for all modules
- Multilingual support infrastructure is essential from the start
- Academic year and class management form the foundation for all academic features

## Core Academic Features (Phases 2-4)
- Class and subject management must precede student enrollment
- Student enrollment is required before attendance tracking
- Basic grade management must be implemented before advanced reporting
- Teacher assignment system is needed before class scheduling

## Service Modules (Phases 5-7)
- Online learning platform can be developed in parallel with student services
- Transportation and cafeteria services are independent modules that can be built concurrently
- Financial management depends on service configuration being established first

## Communication Layer (Phase 8)
- Notification system should be implemented after core features are stable
- Real-time updates enhance existing features rather than creating new ones
- Communication features integrate with all previous modules

## Optimization and Integration (Phases 9-10)
- Advanced features build upon established core functionality
- Performance optimization requires a complete system to test against
- External integrations are final layer that enhances existing capabilities

# Risks and Mitigations  
## Technical Challenges
- **Multilingual Complexity**: Arabic RTL and French LTR support requires careful UI/UX design and testing
  - Mitigation: Implement multilingual support from day one, use established libraries, extensive testing
- **Real-time Performance**: Large number of concurrent users may impact system performance
  - Mitigation: Implement caching strategies, optimize database queries, use Laravel Reverb efficiently
- **Data Migration**: Migrating from existing systems may be complex
  - Mitigation: Develop robust import/export tools, thorough data validation, phased migration approach

## MVP Definition
- **Core MVP**: Authentication, basic academic management (classes, subjects, students), simple grade entry, and basic reporting
- **Usable Frontend**: Focus on teacher and admin dashboards first, as they drive most system usage
- **Incremental Value**: Each phase should deliver tangible value to end users

## Resource Constraints
- **Development Complexity**: Large scope requires careful project management
  - Mitigation: Clear phase definitions, regular milestone reviews, modular development approach
- **Testing Requirements**: Multilingual and multi-role system requires extensive testing
  - Mitigation: Automated testing implementation, user acceptance testing for each phase

# Appendix  
## Technical Specifications
- Laravel 11.x with Inertia.js for seamless SPA experience
- React with TypeScript for type safety and better development experience
- Tailwind CSS and Shadcn UI for consistent, modern design system
- Zustand for client-side state management
- MySQL/MariaDB for reliable data storage
- Laravel Reverb for real-time capabilities

## Security Requirements
- HTTPS enforcement for all communications
- Strong authentication with password policies
- Role-based access control with granular permissions
- Input validation and sanitization
- Regular security audits and updates
- OWASP compliance

## Performance Requirements
- Support for 1000+ concurrent users
- Page load times under 2 seconds
- Real-time updates with minimal latency
- Efficient database query optimization
- Caching strategies for frequently accessed data
- Mobile-responsive design for various devices
</PRD>

# Task ID: 2
# Title: Configure Database and Create Initial Schema
# Status: done
# Dependencies: 1
# Priority: high
# Description: Configure the database connection settings in `.env` to use MySQL/MariaDB. Create the initial database schema based on the defined data models.
# Details:
Update `.env` with database credentials. Create migrations for core entities like `users`, `schools`, `academic_years`, `classes`, `subjects`, and `students`. Use Lara<PERSON>'s migration system to apply the schema to the database.

# Test Strategy:
Ensure that the database connection is successful and that all migrations run without errors. Verify the schema in the database.

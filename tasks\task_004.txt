# Task ID: 4
# Title: Implement Basic School Configuration
# Status: done
# Dependencies: 3
# Priority: medium
# Description: Set up the basic school configuration settings, including school name, address, and other relevant information. Create a settings management interface for administrators. Implementation completed with multilingual support, comprehensive CRUD operations, and a React/TypeScript frontend.
# Details:
The school configuration system is now fully functional. The implementation includes a `schools` table with multilingual fields (name, description, address in AR/FR/EN), an admin interface to manage school settings, and storage of settings in the database. Key features include multilingual support, academic year configuration, contact information management, address management, logo upload, and system settings.

# Test Strategy:
Comprehensive testing has been completed using Pest. The test suite covers page access, CRUD operations, file uploads, and validation. All tests are passing. Verify the settings are displayed correctly in the admin interface and that multilingual support functions as expected.

# Subtasks:
## 4.1. undefined [completed]
### Dependencies: None
### Description: Create School model with multilingual fields (name, description, address in AR/FR/EN).
### Details:


## 4.2. undefined [completed]
### Dependencies: None
### Description: Implement migration with academic settings, contact info, and system configuration fields.
### Details:


## 4.3. undefined [completed]
### Dependencies: None
### Description: Add model attributes for localized content based on current locale.
### Details:


## 4.4. undefined [completed]
### Dependencies: None
### Description: Create SchoolFactory for testing.
### Details:


## 4.5. undefined [completed]
### Dependencies: None
### Description: Add SchoolSeeder with default Tunisian school data.
### Details:


## 4.6. undefined [completed]
### Dependencies: None
### Description: Implement SchoolSettingsController with full CRUD operations.
### Details:


## 4.7. undefined [completed]
### Dependencies: None
### Description: Add comprehensive validation for all fields including multilingual support.
### Details:


## 4.8. undefined [completed]
### Dependencies: None
### Description: Implement file upload handling for school logos with storage management.
### Details:


## 4.9. undefined [completed]
### Dependencies: None
### Description: Add proper error handling and success messages.
### Details:


## 4.1. undefined [completed]
### Dependencies: None
### Description: Create comprehensive React/TypeScript component using shadcn/ui.
### Details:


## 4.11. undefined [completed]
### Dependencies: None
### Description: Implement multilingual form fields with RTL support for Arabic.
### Details:


## 4.12. undefined [completed]
### Dependencies: None
### Description: Add file upload interface with preview and removal functionality.
### Details:


## 4.13. undefined [completed]
### Dependencies: None
### Description: Organize settings into logical sections: Basic Info, Address, Academic Settings, Language & System Settings.
### Details:


## 4.14. undefined [completed]
### Dependencies: None
### Description: Integrate with Inertia.js for seamless SPA experience.
### Details:


## 4.15. undefined [completed]
### Dependencies: None
### Description: Add admin routes with proper middleware protection.
### Details:


## 4.16. undefined [completed]
### Dependencies: None
### Description: Configure route names for easy access (admin.school-settings).
### Details:


## 4.17. undefined [completed]
### Dependencies: None
### Description: Create comprehensive Pest test suite with 4 test cases covering page access, CRUD operations, file uploads, and validation.
### Details:


## 4.18. undefined [completed]
### Dependencies: None
### Description: Verify all tests are passing (4 passed, 27 assertions).
### Details:


## 5.9. Frontend Layout Integration [completed]
### Dependencies: None
### Description: Update SchoolSettings page to use proper AppLayout instead of AppShell, add breadcrumb navigation, and integrate with sidebar navigation structure
### Details:
- Replace AppShell with AppLayout component
- Add breadcrumb navigation (Dashboard > Admin > School Settings)
- Add proper padding and spacing for content
- Integrate School Settings into admin navigation menu
- Ensure consistent layout structure across admin pages

## 6.9. Navigation Menu Integration [completed]
### Dependencies: None
### Description: Add comprehensive admin navigation menu structure to app-sidebar.tsx with proper grouping and icons
### Details:
- Add Administration navigation group with School Settings, Academic Management, User Management, System Settings
- Add Academic navigation group with Classes, Subjects, Students, Teachers, Grades, Attendance
- Update NavMain component to support group labels and conditional rendering
- Add appropriate Lucide icons for each navigation item
- Ensure proper navigation structure and organization


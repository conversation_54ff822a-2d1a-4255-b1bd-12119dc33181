# Task ID: 7
# Title: Implement Academic Year and Term Management
# Status: pending
# Dependencies: 2, 3
# Priority: medium
# Description: Implement academic year and period/term management functionality. Allow administrators to create, update, and delete academic years and terms.
# Details:
Create `academic_years` and `periods` tables in the database. Develop an admin interface to manage academic years and terms. Implement CRUD operations for these entities.

# Test Strategy:
Verify that academic years and terms can be created, updated, and deleted correctly. Ensure that the admin interface is user-friendly.

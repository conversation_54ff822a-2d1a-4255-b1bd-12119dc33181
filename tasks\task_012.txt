# Task ID: 12
# Title: Implement Basic Grade Entry and Viewing
# Status: done
# Dependencies: 10, 11
# Priority: medium
# Description: Implement a comprehensive grade entry and viewing system with features for managing student assessments and grades. Teachers can enter grades for students, and students can view their grades with performance analytics.
# Details:
The system includes a `grades` table with foreign key relationships to schools, academic years, students, teachers, classes, and subjects. It features a teacher interface for CRUD operations with role-based access control and a student interface for read-only access to published grades. Implements validation to ensure that grades are within valid ranges and prevents duplicate entries. Includes automatic calculation of percentage, letter grade, and GPA points.

# Test Strategy:
Comprehensive test suite verifies teacher access control, non-teacher access restriction, teacher grade creation for assigned classes, student grade viewing (published only), and automatic calculation verification (percentage and letter grades).

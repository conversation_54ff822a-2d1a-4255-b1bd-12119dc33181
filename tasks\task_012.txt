# Task ID: 12
# Title: Implement Basic Grade Entry and Viewing
# Status: pending
# Dependencies: 10, 11
# Priority: medium
# Description: Implement a simple grade entry and viewing system. Allow teachers to enter grades for students and allow students to view their grades.
# Details:
Create a `grades` table in the database. Develop a teacher interface to enter grades. Develop a student interface to view grades. Implement validation to ensure that grades are within valid ranges.

# Test Strategy:
Verify that teachers can enter grades correctly. Ensure that students can view their grades. Test validation rules.

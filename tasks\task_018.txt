# Task ID: 18
# Title: Implement Advanced Grade Management
# Status: pending
# Dependencies: 12
# Priority: medium
# Description: Implement advanced grade management with multiple examination types. Allow teachers to create different examination types and enter grades for each type.
# Details:
Create an `examination_types` table in the database. Modify the `grades` table to support multiple examination types. Develop a teacher interface to create examination types and enter grades for each type.

# Test Strategy:
Verify that teachers can create examination types and enter grades correctly. Ensure that the grade data is stored correctly in the database. Test validation rules.

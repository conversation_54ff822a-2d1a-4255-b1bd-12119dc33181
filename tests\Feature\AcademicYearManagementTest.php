<?php

use App\Models\AcademicYear;
use App\Models\Period;
use App\Models\School;
use App\Models\User;

test('admin can view academic years index', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    $response = $this->actingAs($admin)->get('/admin/academic-years');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('Admin/AcademicYears/Index')
            ->has('academicYears')
            ->has('school')
    );
});

test('non-admin cannot access academic years', function () {
    $student = User::factory()->student()->create();

    $response = $this->actingAs($student)->get('/admin/academic-years');

    $response->assertStatus(403);
});

test('admin can create academic year', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    $academicYearData = [
        'name' => '2024-2025',
        'name_ar' => '2024-2025',
        'name_fr' => '2024-2025',
        'description' => 'Academic year 2024-2025',
        'start_date' => '2024-09-01',
        'end_date' => '2025-06-30',
        'is_active' => true,
        'is_current' => false,
    ];

    $response = $this->actingAs($admin)->post('/admin/academic-years', $academicYearData);

    $response->assertRedirect('/admin/academic-years');
    $this->assertDatabaseHas('academic_years', [
        'name' => '2024-2025',
        'school_id' => $school->id,
    ]);
});

test('setting academic year as current unsets others', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    $currentYear = AcademicYear::factory()->create([
        'school_id' => $school->id,
        'is_current' => true,
    ]);

    $newYear = AcademicYear::factory()->create([
        'school_id' => $school->id,
        'is_current' => false,
    ]);

    $response = $this->actingAs($admin)->post("/admin/academic-years/{$newYear->id}/set-current");

    $response->assertRedirect('/admin/academic-years');

    $currentYear->refresh();
    $newYear->refresh();

    expect($currentYear->is_current)->toBeFalse();
    expect($newYear->is_current)->toBeTrue();
});

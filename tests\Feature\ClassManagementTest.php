<?php

use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\User;

test('admin can view classes index', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    $response = $this->actingAs($admin)->get('/admin/classes');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('Admin/Classes/Index')
            ->has('classes')
            ->has('academicYears')
            ->has('gradeLevels')
            ->has('school')
    );
});

test('non-admin cannot access classes', function () {
    $student = User::factory()->student()->create();

    $response = $this->actingAs($student)->get('/admin/classes');

    $response->assertStatus(403);
});

test('admin can create class', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();
    $academicYear = AcademicYear::factory()->create(['school_id' => $school->id]);

    $classData = [
        'academic_year_id' => $academicYear->id,
        'name' => 'Grade 5A',
        'name_ar' => 'الصف الخامس أ',
        'name_fr' => 'CM2 A',
        'description' => 'Grade 5 Section A',
        'grade_level' => 'Grade 5',
        'section' => 'A',
        'capacity' => 30,
        'classroom' => 'Room 101',
        'is_active' => true,
    ];

    $response = $this->actingAs($admin)->post('/admin/classes', $classData);

    $response->assertRedirect('/admin/classes');
    $this->assertDatabaseHas('classes', [
        'name' => 'Grade 5A',
        'school_id' => $school->id,
        'academic_year_id' => $academicYear->id,
    ]);
});

test('cannot create duplicate class name in same academic year', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();
    $academicYear = AcademicYear::factory()->create(['school_id' => $school->id]);

    // Create first class
    SchoolClass::factory()->create([
        'school_id' => $school->id,
        'academic_year_id' => $academicYear->id,
        'name' => 'Grade 5A',
    ]);

    // Try to create duplicate
    $classData = [
        'academic_year_id' => $academicYear->id,
        'name' => 'Grade 5A',
        'grade_level' => 'Grade 5',
        'section' => 'A',
        'capacity' => 30,
        'is_active' => true,
    ];

    $response = $this->actingAs($admin)->post('/admin/classes', $classData);

    $response->assertSessionHasErrors(['name']);
});

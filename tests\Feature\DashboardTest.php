<?php

use App\Models\User;

test('guests are redirected to the login page', function () {
    $this->get('/dashboard')->assertRedirect('/login');
});

test('authenticated users can visit the dashboard', function () {
    $this->actingAs($user = User::factory()->create());

    $this->get('/dashboard')->assertOk();
});

test('admin users see admin dashboard data', function () {
    $admin = User::factory()->admin()->create();

    $response = $this->actingAs($admin)->get('/dashboard');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('dashboard')
            ->has('user')
            ->has('dashboardData')
            ->where('user.role', 'admin')
            ->where('dashboardData.role', 'admin')
            ->has('dashboardData.stats')
            ->has('dashboardData.quick_actions')
            ->has('dashboardData.recent_activities')
    );
});

test('teacher users see teacher dashboard data', function () {
    $teacher = User::factory()->teacher()->create();

    $response = $this->actingAs($teacher)->get('/dashboard');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('dashboard')
            ->has('user')
            ->has('dashboardData')
            ->where('user.role', 'teacher')
            ->where('dashboardData.role', 'teacher')
            ->has('dashboardData.stats')
            ->has('dashboardData.quick_actions')
            ->has('dashboardData.recent_activities')
    );
});

test('student users see student dashboard data', function () {
    $student = User::factory()->student()->create();

    $response = $this->actingAs($student)->get('/dashboard');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('dashboard')
            ->has('user')
            ->has('dashboardData')
            ->where('user.role', 'student')
            ->where('dashboardData.role', 'student')
            ->has('dashboardData.stats')
            ->has('dashboardData.quick_actions')
            ->has('dashboardData.recent_activities')
    );
});

test('parent users see parent dashboard data', function () {
    $parent = User::factory()->parent()->create();

    $response = $this->actingAs($parent)->get('/dashboard');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('dashboard')
            ->has('user')
            ->has('dashboardData')
            ->where('user.role', 'parent')
            ->where('dashboardData.role', 'parent')
            ->has('dashboardData.stats')
            ->has('dashboardData.quick_actions')
            ->has('dashboardData.recent_activities')
    );
});

test('dashboard displays correct stats for admin users', function () {
    $admin = User::factory()->admin()->create();

    $response = $this->actingAs($admin)->get('/dashboard');

    $response->assertInertia(fn ($page) =>
        $page->where('dashboardData.stats.total_users', User::count())
            ->where('dashboardData.stats.total_teachers', User::where('role', 'teacher')->count())
            ->where('dashboardData.stats.total_students', User::where('role', 'student')->count())
            ->where('dashboardData.stats.total_parents', User::where('role', 'parent')->count())
    );
});

test('role middleware protects admin routes', function () {
    $student = User::factory()->student()->create();

    $response = $this->actingAs($student)->get('/admin/school-settings');

    $response->assertStatus(403);
});

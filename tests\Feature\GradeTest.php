<?php

use App\Models\AcademicYear;
use App\Models\Grade;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\StudentEnrollment;
use App\Models\Subject;
use App\Models\TeacherAssignment;
use App\Models\User;

test('teacher can view grades index', function () {
    $teacher = User::factory()->teacher()->create();
    $school = School::factory()->create();

    $response = $this->actingAs($teacher)->get('/teacher/grades');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('Teacher/Grades/Index')
            ->has('grades')
            ->has('academicYears')
            ->has('classes')
            ->has('subjects')
            ->has('gradeTypes')
            ->has('statuses')
    );
});

test('non-teacher cannot access teacher grades', function () {
    $student = User::factory()->student()->create();

    $response = $this->actingAs($student)->get('/teacher/grades');

    $response->assertStatus(403);
});

test('teacher can create grade for assigned class', function () {
    $teacher = User::factory()->teacher()->create();
    $school = School::factory()->create();
    $academicYear = AcademicYear::factory()->create(['school_id' => $school->id]);
    $student = User::factory()->student()->create();
    $class = SchoolClass::factory()->create(['school_id' => $school->id]);
    $subject = Subject::factory()->create(['school_id' => $school->id]);

    // Create teacher assignment
    TeacherAssignment::factory()->create([
        'school_id' => $school->id,
        'academic_year_id' => $academicYear->id,
        'teacher_id' => $teacher->id,
        'class_id' => $class->id,
        'subject_id' => $subject->id,
        'is_active' => true,
    ]);

    // Create student enrollment
    StudentEnrollment::factory()->create([
        'school_id' => $school->id,
        'academic_year_id' => $academicYear->id,
        'student_id' => $student->id,
        'class_id' => $class->id,
        'status' => 'enrolled',
    ]);

    $gradeData = [
        'academic_year_id' => $academicYear->id,
        'student_id' => $student->id,
        'class_id' => $class->id,
        'subject_id' => $subject->id,
        'grade_type' => 'assignment',
        'title' => 'Chapter 1 Assignment',
        'description' => 'Complete exercises 1-10',
        'score' => 85,
        'max_score' => 100,
        'assessment_date' => '2024-01-15',
        'weight' => 1.0,
        'status' => 'published',
        'is_extra_credit' => false,
        'is_makeup' => false,
    ];

    $response = $this->actingAs($teacher)->post('/teacher/grades', $gradeData);

    $response->assertRedirect('/teacher/grades');
    $this->assertDatabaseHas('grades', [
        'teacher_id' => $teacher->id,
        'student_id' => $student->id,
        'title' => 'Chapter 1 Assignment',
        'score' => 85,
        'max_score' => 100,
    ]);
});

test('student can view their published grades', function () {
    $student = User::factory()->student()->create();
    $school = School::factory()->create();

    $response = $this->actingAs($student)->get('/student/grades');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('Student/Grades/Index')
            ->has('grades')
            ->has('academicYears')
            ->has('subjects')
            ->has('gradeTypes')
    );
});

test('grade auto-calculates percentage and letter grade', function () {
    $grade = Grade::factory()->create([
        'score' => 85,
        'max_score' => 100,
        'percentage' => null, // Will be auto-calculated
        'letter_grade' => null, // Will be auto-calculated
    ]);

    expect($grade->percentage)->toBe('85.00');
    expect($grade->letter_grade)->toBe('B');
});

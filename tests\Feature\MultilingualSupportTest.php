<?php

use App\Models\School;
use App\Models\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

beforeEach(function () {
    // Create a school with supported languages
    School::factory()->create([
        'supported_languages' => ['en', 'ar', 'fr'],
        'default_language' => 'ar',
    ]);
});

test('middleware sets locale from URL parameter', function () {
    $this->get('/?locale=fr');

    expect(App::getLocale())->toBe('fr');
    expect(Session::get('locale'))->toBe('fr');
});

test('middleware sets locale from session', function () {
    Session::put('locale', 'ar');

    $this->get('/');

    expect(App::getLocale())->toBe('ar');
});

test('middleware uses school default language when no preference set', function () {
    $this->get('/');

    expect(App::getLocale())->toBe('ar');
});

test('middleware rejects unsupported language', function () {
    $this->get('/?locale=es');

    expect(App::getLocale())->not->toBe('es');
});

test('language controller can switch language', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->get(route('language.switch', ['locale' => 'fr']));

    $response->assertRedirect();
    expect(Session::get('locale'))->toBe('fr');
});

test('language controller updates user preferred language', function () {
    $user = User::factory()->create(['preferred_language' => 'en']);

    $response = $this->actingAs($user)->get(route('language.switch', ['locale' => 'ar']));

    $response->assertRedirect();
    $user->refresh();
    expect($user->preferred_language)->toBe('ar');
});

test('language controller rejects invalid language', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->get(route('language.switch', ['locale' => 'invalid']));

    $response->assertRedirect();
    $response->assertSessionHas('error');
});

test('api returns available languages', function () {
    $response = $this->get(route('api.languages'));

    $response->assertStatus(200);
    $response->assertJsonStructure([
        'languages' => [
            '*' => [
                'code',
                'name',
                'native_name',
                'direction',
            ]
        ],
        'current'
    ]);

    $data = $response->json();
    expect($data['languages'])->toHaveCount(3);
    expect($data['current'])->toBe('ar');
});

test('rtl languages are detected correctly', function () {
    $response = $this->get(route('api.languages'));
    $data = $response->json();

    $arabicLang = collect($data['languages'])->firstWhere('code', 'ar');
    $frenchLang = collect($data['languages'])->firstWhere('code', 'fr');

    expect($arabicLang['direction'])->toBe('rtl');
    expect($frenchLang['direction'])->toBe('ltr');
});

test('middleware uses user preferred language when authenticated', function () {
    $user = User::factory()->create(['preferred_language' => 'fr']);

    $this->actingAs($user)->get('/');

    expect(App::getLocale())->toBe('fr');
    expect(Session::get('locale'))->toBe('fr');
});

test('translations are shared with inertia', function () {
    $user = User::factory()->create(['preferred_language' => 'en']);
    $response = $this->actingAs($user)->get(route('admin.school-settings'));

    $response->assertInertia(fn ($page) =>
        $page->has('translations')
             ->has('locale')
             ->where('locale', 'en')
    );
});

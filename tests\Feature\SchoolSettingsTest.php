<?php

use App\Models\School;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

beforeEach(function () {
    Storage::fake('public');
});

test('authenticated user can view school settings page', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->get(route('admin.school-settings'));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('Admin/SchoolSettings'));
});

test('can create school settings', function () {
    $user = User::factory()->create();

    $schoolData = [
        'name' => 'Test School',
        'name_ar' => 'مدرسة الاختبار',
        'name_fr' => 'École de Test',
        'description' => 'A test school',
        'email' => '<EMAIL>',
        'phone' => '+216 71 123 456',
        'address' => '123 Test Street',
        'city' => 'Tunis',
        'country' => 'Tunisia',
        'academic_year_start_month' => 'September',
        'academic_year_end_month' => 'June',
        'terms_per_year' => 3,
        'default_language' => 'ar',
        'supported_languages' => ['ar', 'fr'],
        'timezone' => 'Africa/Tunis',
        'currency' => 'TND',
        'is_active' => true,
    ];

    $response = $this->actingAs($user)->post(route('admin.school-settings.store'), $schoolData);

    $response->assertRedirect();
    $this->assertDatabaseHas('schools', [
        'name' => 'Test School',
        'email' => '<EMAIL>',
        'city' => 'Tunis',
    ]);
});

test('can upload school logo', function () {
    $user = User::factory()->create();
    $file = UploadedFile::fake()->image('logo.png', 200, 200);

    $schoolData = [
        'name' => 'Test School',
        'address' => '123 Test Street',
        'city' => 'Tunis',
        'country' => 'Tunisia',
        'academic_year_start_month' => 'September',
        'academic_year_end_month' => 'June',
        'terms_per_year' => 3,
        'default_language' => 'ar',
        'supported_languages' => ['ar', 'fr'],
        'timezone' => 'Africa/Tunis',
        'currency' => 'TND',
        'logo' => $file,
        'is_active' => true,
    ];

    $response = $this->actingAs($user)->post(route('admin.school-settings.store'), $schoolData);

    $response->assertRedirect();

    $school = School::first();
    expect($school->logo_path)->not->toBeNull();
    expect(Storage::disk('public')->exists($school->logo_path))->toBeTrue();
});

test('validates required fields', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->post(route('admin.school-settings.store'), []);

    $response->assertSessionHasErrors([
        'name',
        'address',
        'city',
        'country',
        'academic_year_start_month',
        'academic_year_end_month',
        'terms_per_year',
        'default_language',
        'supported_languages',
        'timezone',
        'currency',
    ]);
});

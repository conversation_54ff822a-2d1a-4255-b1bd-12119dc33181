<?php

use App\Models\AcademicYear;
use App\Models\School;
use App\Models\SchoolClass;
use App\Models\StudentEnrollment;
use App\Models\User;

test('admin can view student enrollments index', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    $response = $this->actingAs($admin)->get('/admin/student-enrollments');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('Admin/StudentEnrollments/Index')
            ->has('enrollments')
            ->has('academicYears')
            ->has('classes')
            ->has('statuses')
            ->has('stats')
            ->has('school')
    );
});

test('non-admin cannot access student enrollments', function () {
    $student = User::factory()->student()->create();

    $response = $this->actingAs($student)->get('/admin/student-enrollments');

    $response->assertStatus(403);
});

test('admin can create student enrollment', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();
    $academicYear = AcademicYear::factory()->create(['school_id' => $school->id]);
    $student = User::factory()->student()->create();
    $class = SchoolClass::factory()->create([
        'school_id' => $school->id,
        'capacity' => 30
    ]);

    $enrollmentData = [
        'academic_year_id' => $academicYear->id,
        'student_id' => $student->id,
        'class_id' => $class->id,
        'enrollment_date' => '2024-01-01',
        'status' => 'enrolled',
        'notes' => 'New student enrollment',
        'is_repeating' => false,
        'emergency_contacts' => [
            [
                'name' => 'John Doe',
                'relationship' => 'Father',
                'phone' => '************',
                'email' => '<EMAIL>',
            ]
        ],
    ];

    $response = $this->actingAs($admin)->post('/admin/student-enrollments', $enrollmentData);

    $response->assertRedirect('/admin/student-enrollments');
    $this->assertDatabaseHas('student_enrollments', [
        'school_id' => $school->id,
        'student_id' => $student->id,
        'class_id' => $class->id,
        'status' => 'enrolled',
    ]);
});

test('cannot create duplicate student enrollment', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();
    $academicYear = AcademicYear::factory()->create(['school_id' => $school->id]);
    $student = User::factory()->student()->create();
    $class = SchoolClass::factory()->create(['school_id' => $school->id]);

    // Create first enrollment
    StudentEnrollment::factory()->create([
        'school_id' => $school->id,
        'academic_year_id' => $academicYear->id,
        'student_id' => $student->id,
        'class_id' => $class->id,
    ]);

    // Try to create duplicate
    $enrollmentData = [
        'academic_year_id' => $academicYear->id,
        'student_id' => $student->id,
        'class_id' => $class->id,
        'enrollment_date' => '2024-01-01',
        'status' => 'enrolled',
        'is_repeating' => false,
    ];

    $response = $this->actingAs($admin)->post('/admin/student-enrollments', $enrollmentData);

    $response->assertSessionHasErrors(['student_id']);
});

<?php

use App\Models\School;
use App\Models\Subject;
use App\Models\User;

test('admin can view subjects index', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    $response = $this->actingAs($admin)->get('/admin/subjects');

    $response->assertOk();
    $response->assertInertia(fn ($page) =>
        $page->component('Admin/Subjects/Index')
            ->has('subjects')
            ->has('categories')
            ->has('gradeLevels')
            ->has('school')
    );
});

test('non-admin cannot access subjects', function () {
    $student = User::factory()->student()->create();

    $response = $this->actingAs($student)->get('/admin/subjects');

    $response->assertStatus(403);
});

test('admin can create subject', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    $subjectData = [
        'name' => 'Mathematics',
        'name_ar' => 'الرياضيات',
        'name_fr' => 'Mathématiques',
        'code' => 'MATH101',
        'description' => 'Core mathematics subject',
        'grade_levels' => ['Grade 1', 'Grade 2', 'Grade 3'],
        'coefficient' => 3.0,
        'weekly_hours' => 5,
        'category' => 'core',
        'is_active' => true,
    ];

    $response = $this->actingAs($admin)->post('/admin/subjects', $subjectData);

    $response->assertRedirect('/admin/subjects');
    $this->assertDatabaseHas('subjects', [
        'name' => 'Mathematics',
        'school_id' => $school->id,
        'code' => 'MATH101',
        'category' => 'core',
    ]);
});

test('cannot create duplicate subject name in same school', function () {
    $admin = User::factory()->admin()->create();
    $school = School::factory()->create();

    // Create first subject
    Subject::factory()->create([
        'school_id' => $school->id,
        'name' => 'Mathematics',
    ]);

    // Try to create duplicate
    $subjectData = [
        'name' => 'Mathematics',
        'coefficient' => 3.0,
        'weekly_hours' => 5,
        'category' => 'core',
        'is_active' => true,
    ];

    $response = $this->actingAs($admin)->post('/admin/subjects', $subjectData);

    $response->assertSessionHasErrors(['name']);
});

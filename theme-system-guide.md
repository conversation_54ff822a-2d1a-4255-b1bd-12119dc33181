# Building a Theme Preset System for React Applications

This guide will walk you through implementing a comprehensive theme system for your React application, including preset management, theme switching, and customization.

## Table of Contents

1. [Setting Up Theme State Management with Zustand](#1-setting-up-theme-state-management-with-zustand)
2. [Defining Theme Presets](#2-defining-theme-presets)
3. [Creating a Theme Provider Component](#3-creating-a-theme-provider-component)
4. [Building a Theme Preset Selector](#4-building-a-theme-preset-selector)
5. [Implementing Theme Switching](#5-implementing-theme-switching)
6. [Applying Themes with CSS Variables](#6-applying-themes-with-css-variables)
7. [Saving and Loading Custom Themes](#7-saving-and-loading-custom-themes)

## 1. Setting Up Theme State Management with Zustand

First, let's create a state management system using Zustand to handle theme presets and the current theme state.

### Install Dependencies

```bash
npm install zustand @types/react @types/react-dom
```

### Create Theme Types

```typescript
// types/theme.ts
export interface ThemeStyleProps {
  // Base colors
  background: string;
  foreground: string;
  
  // Component colors
  primary: string;
  "primary-foreground": string;
  secondary: string;
  "secondary-foreground": string;
  accent: string;
  "accent-foreground": string;
  muted: string;
  "muted-foreground": string;
  
  // UI element colors
  card: string;
  "card-foreground": string;
  popover: string;
  "popover-foreground": string;
  border: string;
  input: string;
  ring: string;
  
  // Utility colors
  destructive: string;
  "destructive-foreground": string;
  
  // Typography
  "font-sans": string;
  "font-serif": string;
  "font-mono": string;
  
  // Other properties
  radius: string;
}

export interface ThemeStyles {
  light: ThemeStyleProps;
  dark: ThemeStyleProps;
}

export interface ThemePreset {
  label: string;
  source?: "SAVED" | "BUILT_IN";
  createdAt?: string;
  styles: ThemeStyles;
}

export interface ThemeEditorState {
  styles: ThemeStyles;
  currentMode: "light" | "dark";
  preset?: string;
}
```

### Create Default Theme Configuration

```typescript
// config/theme.ts
import { ThemeEditorState, ThemeStyleProps } from "../types/theme";

export const defaultLightThemeStyles: ThemeStyleProps = {
  background: "#ffffff",
  foreground: "#333333",
  card: "#ffffff",
  "card-foreground": "#333333",
  popover: "#ffffff",
  "popover-foreground": "#333333",
  primary: "#3b82f6",
  "primary-foreground": "#ffffff",
  secondary: "#f3f4f6",
  "secondary-foreground": "#4b5563",
  muted: "#f9fafb",
  "muted-foreground": "#6b7280",
  accent: "#e0f2fe",
  "accent-foreground": "#1e3a8a",
  destructive: "#ef4444",
  "destructive-foreground": "#ffffff",
  border: "#e5e7eb",
  input: "#e5e7eb",
  ring: "#3b82f6",
  radius: "0.375rem",
  "font-sans": "Inter, sans-serif",
  "font-serif": "Georgia, serif",
  "font-mono": "Menlo, monospace",
};

export const defaultDarkThemeStyles: ThemeStyleProps = {
  background: "#171717",
  foreground: "#e5e5e5",
  card: "#262626",
  "card-foreground": "#e5e5e5",
  popover: "#262626",
  "popover-foreground": "#e5e5e5",
  primary: "#3b82f6",
  "primary-foreground": "#ffffff",
  secondary: "#262626",
  "secondary-foreground": "#e5e5e5",
  muted: "#262626",
  "muted-foreground": "#a3a3a3",
  accent: "#1e3a8a",
  "accent-foreground": "#bfdbfe",
  destructive: "#ef4444",
  "destructive-foreground": "#ffffff",
  border: "#404040",
  input: "#404040",
  ring: "#3b82f6",
  radius: "0.375rem",
  "font-sans": "Inter, sans-serif",
  "font-serif": "Georgia, serif",
  "font-mono": "Menlo, monospace",
};

export const defaultThemeState: ThemeEditorState = {
  styles: {
    light: defaultLightThemeStyles,
    dark: defaultDarkThemeStyles,
  },
  currentMode: 
    typeof window !== "undefined" && window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light",
};

// Common styles that should be applied to both light and dark modes
export const COMMON_STYLES = [
  "radius",
  "font-sans",
  "font-serif",
  "font-mono",
];
```

### Create Theme Preset Store

```typescript
// store/theme-preset-store.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ThemePreset } from "../types/theme";
import { defaultPresets } from "../utils/theme-presets";

interface ThemePresetStore {
  presets: Record<string, ThemePreset>;
  registerPreset: (name: string, preset: ThemePreset) => void;
  unregisterPreset: (name: string) => void;
  updatePreset: (name: string, preset: ThemePreset) => void;
  getPreset: (name: string) => ThemePreset | undefined;
  getAllPresets: () => Record<string, ThemePreset>;
  loadSavedPresets: () => Promise<void>;
}

export const useThemePresetStore = create<ThemePresetStore>()(
  persist(
    (set, get) => ({
      presets: defaultPresets,
      registerPreset: (name: string, preset: ThemePreset) => {
        set((state) => ({
          presets: {
            ...state.presets,
            [name]: preset,
          },
        }));
      },
      unregisterPreset: (name: string) => {
        set((state) => {
          const { [name]: _, ...remainingPresets } = state.presets;
          return {
            presets: remainingPresets,
          };
        });
      },
      updatePreset: (name: string, preset: ThemePreset) => {
        set((state) => ({
          presets: {
            ...state.presets,
            [name]: preset,
          },
        }));
      },
      getPreset: (name: string) => {
        return get().presets[name];
      },
      getAllPresets: () => {
        return get().presets;
      },
      loadSavedPresets: async () => {
        try {
          // This would be replaced with your API call to load saved themes
          const savedThemes = await fetch('/api/themes').then(res => res.json());
          
          const savedPresets = savedThemes.reduce((acc: Record<string, ThemePreset>, theme: any) => {
            acc[theme.id] = {
              label: theme.name,
              styles: theme.styles,
              source: "SAVED",
              createdAt: theme.createdAt,
            };
            return acc;
          }, {});

          set((state) => ({
            presets: {
              ...state.presets,
              ...savedPresets,
            },
          }));
        } catch (error) {
          console.error("Failed to load saved presets:", error);
        }
      },
    }),
    {
      name: "theme-presets-storage",
    }
  )
);
```

### Create Editor Store for Theme State

```typescript
// store/editor-store.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ThemeEditorState } from "../types/theme";
import { defaultThemeState } from "../config/theme";
import { getPresetThemeStyles } from "../utils/theme-preset-helper";
import { isDeepEqual } from "../utils/deep-equal";

interface EditorStore {
  themeState: ThemeEditorState;
  themeCheckpoint: ThemeEditorState | null;
  setThemeState: (state: ThemeEditorState) => void;
  applyThemePreset: (preset: string) => void;
  hasUnsavedChanges: () => boolean;
}

export const useEditorStore = create<EditorStore>()(
  persist(
    (set, get) => ({
      themeState: defaultThemeState,
      themeCheckpoint: null,
      setThemeState: (state: ThemeEditorState) => {
        set({ themeState: state });
      },
      applyThemePreset: (preset: string) => {
        const themeState = get().themeState;
        const newStyles = getPresetThemeStyles(preset);
        const newThemeState: ThemeEditorState = {
          ...themeState,
          preset,
          styles: newStyles,
        };
        set({
          themeState: newThemeState,
          themeCheckpoint: newThemeState,
        });
      },
      hasUnsavedChanges: () => {
        const themeState = get().themeState;
        if (!themeState.preset) return false;
        
        const presetThemeStyles = getPresetThemeStyles(themeState.preset);
        return !isDeepEqual(themeState.styles, presetThemeStyles);
      },
    }),
    {
      name: "editor-storage",
    }
  )
);
```

## 2. Defining Theme Presets

Create a file to store your theme presets:

```typescript
// utils/theme-presets.ts
import { ThemePreset } from "../types/theme";

export const defaultPresets: Record<string, ThemePreset> = {
  "modern-minimal": {
    label: "Modern Minimal",
    source: "BUILT_IN",
    styles: {
      light: {
        background: "#ffffff",
        foreground: "#333333",
        card: "#ffffff",
        "card-foreground": "#333333",
        popover: "#ffffff",
        "popover-foreground": "#333333",
        primary: "#3b82f6",
        "primary-foreground": "#ffffff",
        secondary: "#f3f4f6",
        "secondary-foreground": "#4b5563",
        muted: "#f9fafb",
        "muted-foreground": "#6b7280",
        accent: "#e0f2fe",
        "accent-foreground": "#1e3a8a",
        destructive: "#ef4444",
        "destructive-foreground": "#ffffff",
        border: "#e5e7eb",
        input: "#e5e7eb",
        ring: "#3b82f6",
        radius: "0.375rem",
        "font-sans": "Inter, sans-serif",
        "font-serif": "Georgia, serif",
        "font-mono": "Menlo, monospace",
      },
      dark: {
        background: "#171717",
        foreground: "#e5e5e5",
        card: "#262626",
        "card-foreground": "#e5e5e5",
        popover: "#262626",
        "popover-foreground": "#e5e5e5",
        primary: "#3b82f6",
        "primary-foreground": "#ffffff",
        secondary: "#262626",
        "secondary-foreground": "#e5e5e5",
        muted: "#262626",
        "muted-foreground": "#a3a3a3",
        accent: "#1e3a8a",
        "accent-foreground": "#bfdbfe",
        destructive: "#ef4444",
        "destructive-foreground": "#ffffff",
        border: "#404040",
        input: "#404040",
        ring: "#3b82f6",
        radius: "0.375rem",
        "font-sans": "Inter, sans-serif",
        "font-serif": "Georgia, serif",
        "font-mono": "Menlo, monospace",
      },
    },
  },
  
  "dark-mode": {
    label: "Dark Mode",
    source: "BUILT_IN",
    styles: {
      light: {
        background: "#f8f9fa",
        foreground: "#212529",
        card: "#ffffff",
        "card-foreground": "#212529",
        popover: "#ffffff",
        "popover-foreground": "#212529",
        primary: "#7952b3",
        "primary-foreground": "#ffffff",
        secondary: "#e9ecef",
        "secondary-foreground": "#495057",
        muted: "#f1f3f5",
        "muted-foreground": "#6c757d",
        accent: "#e9d8fd",
        "accent-foreground": "#44337a",
        destructive: "#dc3545",
        "destructive-foreground": "#ffffff",
        border: "#dee2e6",
        input: "#dee2e6",
        ring: "#7952b3",
        radius: "0.375rem",
        "font-sans": "Inter, sans-serif",
        "font-serif": "Georgia, serif",
        "font-mono": "Menlo, monospace",
      },
      dark: {
        background: "#121212",
        foreground: "#e0e0e0",
        card: "#1e1e1e",
        "card-foreground": "#e0e0e0",
        popover: "#1e1e1e",
        "popover-foreground": "#e0e0e0",
        primary: "#bb86fc",
        "primary-foreground": "#121212",
        secondary: "#2c2c2c",
        "secondary-foreground": "#e0e0e0",
        muted: "#2c2c2c",
        "muted-foreground": "#a0a0a0",
        accent: "#3700b3",
        "accent-foreground": "#e0e0e0",
        destructive: "#cf6679",
        "destructive-foreground": "#121212",
        border: "#404040",
        input: "#404040",
        ring: "#bb86fc",
        radius: "0.375rem",
        "font-sans": "Inter, sans-serif",
        "font-serif": "Georgia, serif",
        "font-mono": "Menlo, monospace",
      },
    },
  },
  
  // Add more presets as needed
};
```

Create a helper to get theme styles from presets:

```typescript
// utils/theme-preset-helper.ts
import { defaultThemeState } from "../config/theme";
import { ThemeStyles } from "../types/theme";
import { useThemePresetStore } from "../store/theme-preset-store";

export function getPresetThemeStyles(name: string): ThemeStyles {
  const defaultTheme = defaultThemeState.styles;
  if (name === "default") {
    return defaultTheme;
  }

  const store = useThemePresetStore.getState();
  const preset = store.getPreset(name);
  if (!preset) {
    return defaultTheme;
  }

  return {
    light: {
      ...defaultTheme.light,
      ...(preset.styles.light || {}),
    },
    dark: {
      ...defaultTheme.dark,
      ...(preset.styles.dark || {}),
    },
  };
}

// Utility for deep equality check
export function isDeepEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true;
  if (obj1 == null || obj2 == null) return false;
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!isDeepEqual(obj1[key], obj2[key])) return false;
  }
  
  return true;
}
```

## 3. Creating a Theme Provider Component

```tsx
// components/theme-provider.tsx
import React, { createContext, useContext, useEffect } from "react";
import { useEditorStore } from "../store/editor-store";
import { applyThemeToElement } from "../utils/apply-theme";

type Theme = "dark" | "light";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
};

type Coords = { x: number; y: number };

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: (coords?: Coords) => void;
};

const initialState: ThemeProviderState = {
  theme: "light",
  setTheme: () => null,
  toggleTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { themeState, setThemeState } = useEditorStore();

  useEffect(() => {
    const root = document.documentElement;
    if (!root) return;

    applyThemeToElement(themeState, root);
  }, [themeState]);

  const handleThemeChange = (newMode: Theme) => {
    setThemeState({ ...themeState, currentMode: newMode });
  };

  const handleThemeToggle = (coords?: Coords) => {
    const root = document.documentElement;
    const newMode = themeState.currentMode === "light" ? "dark" : "light";

    // Check if the browser supports view transitions
    const prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;

    if (!document.startViewTransition ||